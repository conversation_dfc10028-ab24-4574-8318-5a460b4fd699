import json
import random
from locust import HttpUser, task, between


class AdminApiUser(HttpUser):
    wait_time = between(1, 5)  # Users will wait between 1 and 5 seconds between tasks
    host = "https://api.credexon.com"  # Default base URL, can be overridden from command line

    # Store IDs fetched from API responses to reuse them
    banner_ids = []
    category_names = []
    cms_ids = []
    contest_ids = []
    football_match_ids = []
    cricket_match_ids = []
    user_ids = []
    subadmin_emails = []  # Using email for subadmin for easier identification
    pool_master_ids = []
    pool_prize_break_ids = []
    download_file_ids = []  # For reports and transactions

    # Authentication token will be stored here
    auth_token = None
    headers = {}

    def on_start(self):
        """
        On start of a Locust user, attempt to log in and obtain an auth token.
        This token will be used for all subsequent authenticated requests.
        """
        self.login()

    def login(self):
        """
        Handles the admin user login to obtain a bearer token.
        Assumes a fixed admin user for testing purposes.
        """
        login_endpoint = "/admin/v1/vendor/login"
        login_payload = {
            "email": "<EMAIL>",  # Replace with your admin email
            "password": "LvYFJfVw*q",  # Replace with your admin password
        }
        with self.client.post(
            login_endpoint, json=login_payload, catch_response=True
        ) as response:
            if response.status_code == 200:
                self.auth_token = response.json().get("data").get("user").get("token")
                if self.auth_token:
                    self.headers = {"Authorization": f"Bearer {self.auth_token}"}
                    response.success()
                    print(
                        f"Successfully logged in and got token: {self.auth_token[:10]}..."
                    )
                else:
                    response.failure("Login successful but no token received")
            else:
                response.failure(
                    f"Login failed with status code {response.status_code}: {response.text}"
                )

    @task(10)  # Higher weight for listing tasks to populate IDs
    def get_list_of_banners(self):
        """
        GET /admin/v1/banner/list
        Fetches the list of banners and stores their IDs for future use.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/banner/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                banners = response.json().get("data", []).get("banner_list", [])
                if banners:
                    for banner in banners:
                        banner_id = banner.get("_id")
                        if banner_id and banner_id not in self.banner_ids:
                            self.banner_ids.append(banner_id)
                response.success()
            else:
                response.failure(f"Failed to get banner list: {response.text}")

    @task(5)
    def create_banner(self):
        """
        POST /admin/v1/banner/save
        Creates a new banner.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        payload = {
            "type": "homepage",
            "image": f"https://example.com/banner_{random.randint(1, 1000)}.jpg",
            "sequence": random.randint(1, 10),
            "status": 1,
            "banner_link": f"https://example.com/link_{random.randint(1, 1000)}",
        }
        with self.client.post(
            "/admin/v1/banner/save",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                # Optionally extract new banner_id if response includes it
                banner_id = response.json().get("data", {}).get("banner_id")
                if banner_id and banner_id not in self.banner_ids:
                    self.banner_ids.append(banner_id)
                response.success()
            else:
                response.failure(f"Failed to create banner: {response.text}")

    @task(2)
    def edit_banner(self):
        """
        POST /admin/v1/banner/edit
        Edits an existing banner using a previously fetched banner_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.banner_ids:
            # If no banners are available, try to get them first
            self.get_list_of_banners()
            if not self.banner_ids:
                print("No banner IDs available for editing.")
                return

        banner_id = random.choice(self.banner_ids)
        payload = {
            "banner_id": banner_id,
            "type": "homepage",
            "image": f"https://example.com/banner_updated_{random.randint(1, 1000)}.jpg",
            "sequence": random.randint(1, 10),
            "device": "mobile",
            "status": 1,
            "banner_link": f"https://example.com/updated_link_{random.randint(1, 1000)}",
        }
        with self.client.post(
            "/admin/v1/banner/edit",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to edit banner {banner_id}: {response.text}")

    @task(10)
    def get_list_of_categories(self):
        """
        GET /admin/v1/category/list
        Fetches the list of categories and stores their names.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/category/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                categories = response.json().get("data", []).get("category_list", [])
                if categories:
                    for category in categories:
                        name = category.get("name")
                        if name and name not in self.category_names:
                            self.category_names.append(name)
                response.success()
            else:
                response.failure(f"Failed to get category list: {response.text}")

    @task(5)
    def create_category(self):
        """
        POST /admin/v1/category/create
        Creates a new category.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        category_name = f"TestCategory_{random.randint(1, 10000)}"
        payload = {
            "name": category_name,
            "description": f"Description for {category_name}",
            "image": f"https://example.com/{category_name}.jpg",
            "status": 1,
        }
        with self.client.post(
            "/admin/v1/category/create",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                if category_name not in self.category_names:
                    self.category_names.append(category_name)
                response.success()
            else:
                response.failure(f"Failed to create category: {response.text}")

    @task(10)
    def get_all_cms_entries(self):
        """
        GET /admin/v1/cms/list
        Fetches all CMS entries and stores their IDs.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/cms/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                cms_entries = response.json().get("data").get("cms_list")
                if cms_entries:
                    for entry in cms_entries:
                        cms_id = entry.get("_id")
                        if cms_id and cms_id not in self.cms_ids:
                            self.cms_ids.append(cms_id)
                response.success()
            else:
                response.failure(f"Failed to get CMS list: {response.text}")

    @task(3)
    def view_cms_entry(self):
        """
        POST /admin/v1/cms/view
        Views a specific CMS entry using a previously fetched cms_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.cms_ids:
            self.get_all_cms_entries()
            if not self.cms_ids:
                print("No CMS IDs available for viewing.")
                return

        cms_id = random.choice(self.cms_ids)
        payload = {"cms_id": cms_id}
        with self.client.post(
            "/admin/v1/cms/view",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to view CMS entry {cms_id}: {response.text}")

    @task(2)
    def update_cms_entry(self):
        """
        PUT /admin/v1/cms/update
        Updates an existing CMS entry using a previously fetched cms_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.cms_ids:
            self.get_all_cms_entries()
            if not self.cms_ids:
                print("No CMS IDs available for updating.")
                return

        cms_id = random.choice(self.cms_ids)
        payload = {
            "cms_id": cms_id,
            "title": f"Updated Title {random.randint(1, 10000)}",
            "slug": f"updated-slug-{random.randint(1, 10000)}",
            "content": f"<p>This is the updated content for CMS ID {cms_id}.</p>",
        }
        with self.client.put(
            "/admin/v1/cms/update",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to update CMS entry {cms_id}: {response.text}"
                )

    @task(5)
    def create_cms_entry(self):
        """
        POST /admin/v1/cms/create
        Creates a new CMS entry.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        title = f"New CMS Entry {random.randint(1, 10000)}"
        payload = {
            "title": title,
            "slug": title.lower().replace(" ", "-"),
            "content": f"<p>This is the content for the new CMS entry: {title}</p>",
        }
        with self.client.post(
            "/admin/v1/cms/create",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                # Optionally extract new cms_id if response includes it
                cms_id = (
                    response.json().get("data", {}).get("cms_id")
                )  # Adjust according to actual response structure
                if cms_id and cms_id not in self.cms_ids:
                    self.cms_ids.append(cms_id)
                response.success()
            else:
                response.failure(f"Failed to create CMS entry: {response.text}")

    @task(10)
    def get_all_contact_us_requests(self):
        """
        GET /admin/v1/contactus/get_all_contact_us_requests
        Retrieves all contact us requests.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/contactus/get_all_contact_us_requests",
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get contact us requests: {response.text}")

    @task(5)
    def create_contest(self):
        """
        POST /admin/v1/contest/create
        Creates a new contest.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        payload = {
            "title": f"Test Contest {random.randint(1, 10000)}",
            "subtitle": "Load test contest",
            "dis_val": random.randint(1, 50),
        }
        with self.client.post(
            "/admin/v1/contest/create",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                contest_id = (
                    response.json().get("data", {}).get("contest_id")
                )  # Adjust if key is different
                if contest_id and contest_id not in self.contest_ids:
                    self.contest_ids.append(contest_id)
                response.success()
            else:
                response.failure(f"Failed to create contest: {response.text}")

    @task(3)
    def view_contest(self):
        """
        POST /admin/v1/contest/view
        Views a specific contest using a previously fetched contest_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.contest_ids:
            self.list_all_contests()  # Try to populate contest_ids
            if not self.contest_ids:
                print("No contest IDs available for viewing.")
                return

        contest_id = random.choice(self.contest_ids)
        payload = {"contest_id": contest_id}
        with self.client.post(
            "/admin/v1/contest/view",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to view contest {contest_id}: {response.text}"
                )

    @task(2)
    def update_contest(self):
        """
        POST /admin/v1/contest/update
        Updates an existing contest using a previously fetched contest_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.contest_ids:
            self.list_all_contests()
            if not self.contest_ids:
                print("No contest IDs available for updating.")
                return

        contest_id = random.choice(self.contest_ids)
        payload = {
            "contest_id": contest_id,
            "title": f"Updated Contest Title {random.randint(1, 10000)}",
            "subtitle": f"Updated Subtitle {random.randint(1, 10000)}",
            "dis_val": random.randint(1, 50),
        }
        with self.client.post(
            "/admin/v1/contest/update",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to update contest {contest_id}: {response.text}"
                )

    @task(1)
    def toggle_contest_status(self):
        """
        POST /admin/v1/contest/active-inactive
        Toggles the active status of a contest.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.contest_ids:
            self.list_all_contests()
            if not self.contest_ids:
                print("No contest IDs available for status toggle.")
                return

        contest_id = random.choice(self.contest_ids)
        status = random.choice([0, 1])  # Randomly set to active or inactive
        payload = {"contest_id": contest_id, "status": status}
        with self.client.post(
            "/admin/v1/contest/active-inactive",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to toggle status for contest {contest_id}: {response.text}"
                )

    @task(10)
    def list_all_contests(self):
        """
        GET /admin/v1/contest/list
        Lists all contests and populates contest_ids.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/contest/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                contests = response.json().get("data", []).get("contest_list", [])
                if contests:
                    for contest in contests:
                        contest_id = contest.get("_id")
                        if contest_id and contest_id not in self.contest_ids:
                            self.contest_ids.append(contest_id)
                response.success()
            else:
                response.failure(f"Failed to list contests: {response.text}")

    @task(10)
    def get_dashboard_list(self):
        """
        GET /admin/v1/dashboard/list
        Retrieves dashboard list.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/dashboard/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get dashboard list: {response.text}")

    @task(10)
    def get_football_match_list(self):
        """
        GET /admin/v1/football/list
        Fetches the list of football matches and stores their IDs.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/football/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                matches = (
                    response.json().get("data", {}).get("match_list", [])
                )  # Adjust if structure is different
                if matches:
                    for match in matches:
                        match_id = match.get("match_id")  # Adjust if key is different
                        if match_id and match_id not in self.football_match_ids:
                            self.football_match_ids.append(match_id)
                response.success()
            else:
                response.failure(f"Failed to get football match list: {response.text}")

    @task(1)
    def activate_deactivate_football_match(self):
        """
        POST /admin/v1/football/active-inactive
        Activates or deactivates a football match.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.football_match_ids:
            self.get_football_match_list()
            if not self.football_match_ids:
                print("No football match IDs available for status change.")
                return

        match_id = random.choice(self.football_match_ids)
        is_active = random.choice([0, 1])
        payload = {"match_id": match_id, "is_active": is_active}
        with self.client.post(
            "/admin/v1/football/active-inactive",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to change football match {match_id} status: {response.text}"
                )

    @task(10)
    def get_cricket_match_list(self):
        """
        GET /admin/v1/match/list
        Fetches the list of cricket matches and stores their IDs.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/match/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                matches = (
                    response.json().get("data", {}).get("match_list", [])
                )  # Adjust if structure is different
                if matches:
                    for match in matches:
                        match_id = match.get("match_id")  # Adjust if key is different
                        if match_id and match_id not in self.cricket_match_ids:
                            self.cricket_match_ids.append(match_id)
                response.success()
            else:
                response.failure(f"Failed to get cricket match list: {response.text}")

    @task(1)
    def activate_deactivate_cricket_match(self):
        """
        POST /admin/v1/match/active-inactive
        Activates or deactivates a cricket match.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.cricket_match_ids:
            self.get_cricket_match_list()
            if not self.cricket_match_ids:
                print("No cricket match IDs available for status change.")
                return

        match_id = random.choice(self.cricket_match_ids)
        is_active = random.choice([0, 1])
        payload = {"match_id": match_id, "is_active": is_active}
        with self.client.post(
            "/admin/v1/match/active-inactive",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to change cricket match {match_id} status: {response.text}"
                )

    @task(1)
    def publish_cricket_match(self):
        """
        POST /admin/v1/match/publish
        Publishes or unpublishes a cricket match.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.cricket_match_ids:
            self.get_cricket_match_list()
            if not self.cricket_match_ids:
                print("No cricket match IDs available for publishing.")
                return

        match_id = random.choice(self.cricket_match_ids)
        is_publish = random.choice([0, 1])
        payload = {"match_id": match_id, "is_publish": is_publish}
        with self.client.post(
            "/admin/v1/match/publish",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to publish cricket match {match_id}: {response.text}"
                )

    @task(10)
    def get_notification_list(self):
        """
        POST /admin/v1/notification/list
        Get list of notifications.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        # This endpoint expects a POST with an empty body or specific filters
        with self.client.post(
            "/admin/v1/notification/list",
            json={},
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get notification list: {response.text}")

    @task(2)
    def send_notification_message(self):
        """
        POST /admin/v1/notification/send_message
        Sends a notification message.
        Requires valid user_ids if targeting specific users.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.user_ids:
            self.get_user_list()  # Try to populate user_ids
            if not self.user_ids:
                print("No user IDs available to send notification.")
                return

        # Pick a random subset of user IDs
        target_users = random.sample(self.user_ids, min(len(self.user_ids), 3))
        payload = {
            "userids": target_users,
            "isexpire": 1,
            "type": "info",
            "body": f"Test notification message from load test {random.randint(1, 1000)}",
            "title": f"Test Notification {random.randint(1, 1000)}",
            "isadmin": 1,
            "enddate": "2025-12-31",  # Example date
        }
        with self.client.post(
            "/admin/v1/notification/send_message",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to send notification: {response.text}")

    @task(10)
    def get_player_accumulator_series_list(self):
        """
        GET /admin/v1/playeraccumulator/series_list
        Retrieves list of series for player accumulator.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/playeraccumulator/series_list",
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to get player accumulator series list: {response.text}"
                )

    @task(10)
    def get_player_list(self):
        """
        POST /admin/v1/plymetadata/player_list
        Get list of players (requires form data).
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        # This endpoint appears to take form-urlencoded, but the swagger shows responses without specific parameters
        # Assuming it can be called with an empty body for general list, or specific filters if known.
        # For dynamic load testing, we'll make a generic call if no specific filtering logic is needed.
        with self.client.post(
            "/admin/v1/plymetadata/player_list",
            headers=self.headers,
            data={},
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get player list: {response.text}")

    @task(5)
    def create_pool_master(self):
        """
        POST /admin/v1/poolmaster/create
        Creates a new pool master entry.
        Note: The schema is generic, so we'll use example data.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        payload = {
            "contest_id": random.choice(self.contest_ids),
            "pool_name": f"Dynamic Pool {random.randint(1, 1000)}",
            "entry_fee": random.uniform(10, 100),
            "prize_amount": random.uniform(500, 5000),
            "max_entries": random.randint(50, 500),
            "game_type": random.choice(["cricket", "football"]),
            "status": 1,
        }
        with self.client.post(
            "/admin/v1/poolmaster/create",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                pool_id = (
                    response.json().get("data", {}).get("pool_id")
                )  # Adjust if key is different
                if pool_id and pool_id not in self.pool_master_ids:
                    self.pool_master_ids.append(pool_id)
                response.success()
            else:
                response.failure(f"Failed to create pool master: {response.text}")

    @task(1)
    def create_pool_range(self):
        """
        POST /admin/v1/poolmaster/pool_range
        Creates a prize range for a pool.
        Requires an existing poolmaster_id.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.pool_master_ids:
            self.get_pool_master_list()
            if not self.pool_master_ids:
                print("No pool master IDs available to create pool range.")
                return

        poolmaster_id = random.choice(self.pool_master_ids)
        pmin = random.randint(1, 5)
        pmax = random.randint(pmin + 1, 10)
        pamount = random.randint(100, 1000)
        payload = {
            "poolmaster_id": poolmaster_id,
            "pmin": pmin,
            "pmax": pmax,
            "pamount": pamount,
        }
        with self.client.post(
            "/admin/v1/poolmaster/pool_range",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                prize_break_id = (
                    response.json().get("data", {}).get("poolprizebreak_id")
                )
                if prize_break_id and prize_break_id not in self.pool_prize_break_ids:
                    self.pool_prize_break_ids.append(prize_break_id)
                response.success()
            else:
                response.failure(
                    f"Failed to create pool range for {poolmaster_id}: {response.text}"
                )

    @task(10)
    def get_pool_master_list(self):
        """
        POST /admin/v1/poolmaster/list
        Gets list of pool masters. This endpoint has required body parameters.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        # These parameters might need to be dynamically fetched or known beforehand for real use cases
        # For now, using example values from swagger.

        contest_data = (
            self.client.get(
                "/admin/v1/contest/list",
                headers=self.headers,
                catch_response=True,
            )
            .json()
            .get("data", {})
        )

        if len(contest_data.get("contest_list", [])) == 0:
            print("No contests available to fetch pool master list.")
            return

        payload = {
            "contest_id": contest_data.get("contest_list", [])[0].get("_id"),
            "gtype": contest_data.get("gtype"),
            "type": "standard",
        }
        with self.client.post(
            "/admin/v1/poolmaster/list",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                pools = response.json().get("data", [])
                if pools:
                    for pool in pools:
                        pool_id = pool.get(
                            "pool_id"
                        )  # Adjust according to actual response structure
                        if pool_id and pool_id not in self.pool_master_ids:
                            self.pool_master_ids.append(pool_id)
                response.success()
            else:
                response.failure(f"Failed to get pool master list: {response.text}")

    @task(10)
    def get_report_downloads_list(self):
        """
        GET /admin/v1/report/get-downloads
        Get list of downloadable files for reports.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/report/get-downloads", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                files = response.json().get("data", [])
                if files:
                    for file_info in files:
                        file_id = file_info.get("fileId")  # Adjust if key is different
                        if file_id and file_id not in self.download_file_ids:
                            self.download_file_ids.append(file_id)
                response.success()
            else:
                response.failure(
                    f"Failed to get report downloads list: {response.text}"
                )

    @task(2)
    def download_report_file(self):
        """
        GET /admin/v1/report/download-file/{fileId}
        Downloads a specific report file.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.download_file_ids:
            self.get_report_downloads_list()
            if not self.download_file_ids:
                print("No report file IDs available for download.")
                return

        file_id = random.choice(self.download_file_ids)
        with self.client.get(
            f"/admin/v1/report/download-file/{file_id}",
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to download report file {file_id}: {response.text}"
                )

    @task(10)
    def get_list_of_states(self):
        """
        GET /admin/v1/statemanger/list
        Get the list of states.
        """
        with self.client.get(
            "/admin/v1/statemanger/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get state list: {response.text}")

    @task(5)
    def create_subadmin_user(self):
        """
        POST /admin/v1/subadmin/create
        Creates a new subadmin user.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        rand_num = random.randint(1, 100000)
        email = f"subadmin_{rand_num}@example.com"
        phone = f"9{random.randint(100000000, 999999999)}"
        payload = {
            "name": f"Subadmin User {rand_num}",
            "email": email,
            "usertype": 2,  # Example user type
            "country_code": "+91",
            "phone": phone,
            "password": "password123",
            "dob": "1990-01-01",
            "gender": "Male",
            "module_data": json.dumps(
                {"User": True, "Cms_Manager": False, "Matches": True}
            ),
        }
        # Use data=payload and content-type header for x-www-form-urlencoded
        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        with self.client.post(
            "/admin/v1/subadmin/create",
            data=payload,
            headers=headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                if email not in self.subadmin_emails:
                    self.subadmin_emails.append(email)
                response.success()
            else:
                response.failure(f"Failed to create subadmin: {response.text}")

    @task(10)
    def get_subadmin_list(self):
        """
        POST /admin/v1/subadmin/list
        Get a list of all subadmin users.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        with self.client.post(
            "/admin/v1/subadmin/list", headers=headers, data={}, catch_response=True
        ) as response:
            if response.status_code == 200:
                # Optionally parse response to get IDs if needed for other tasks
                subadmins = (
                    response.json().get("data", {}).get("subadmin_users", [])
                )  # Adjust based on actual response
                for sa in subadmins:
                    email = sa.get("email")
                    if email and email not in self.subadmin_emails:
                        self.subadmin_emails.append(email)
                response.success()
            else:
                response.failure(f"Failed to get subadmin list: {response.text}")

    @task(10)
    def get_transaction_list(self):
        """
        GET /admin/v1/transaction/list
        Get list of transactions.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/transaction/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get transaction list: {response.text}")

    @task(5)
    def filter_transactions(self):
        """
        POST /admin/v1/transaction/filter
        Filters transactions by date and status.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        payload = {
            "startDate": "2024-01-01",
            "endDate": "2024-01-31",
            "status": random.choice(["completed", "pending", "failed"]),
        }
        with self.client.post(
            "/admin/v1/transaction/filter",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to filter transactions: {response.text}")

    @task(10)
    def get_user_list(self):
        """
        GET /admin/v1/users/list
        Get list of users and stores their IDs.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/users/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                users = (
                    response.json().get("data", {}).get("user_list", [])
                )  # Adjust if structure is different
                if users:
                    for user in users:
                        user_id = user.get("id")  # Adjust if key is different
                        if user_id and user_id not in self.user_ids:
                            self.user_ids.append(user_id)
                response.success()
            else:
                response.failure(f"Failed to get user list: {response.text}")

    @task(2)
    def view_user_details(self):
        """
        POST /admin/v1/users/view
        Views details of a specific user.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.user_ids:
            self.get_user_list()
            if not self.user_ids:
                print("No user IDs available for viewing.")
                return

        user_id = random.choice(self.user_ids)
        payload = {"id": user_id}
        with self.client.post(
            "/admin/v1/users/view",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to view user {user_id} details: {response.text}"
                )

    @task(1)
    def update_user_status(self):
        """
        POST /admin/v1/users/update-user
        Updates a user's status (active/inactive).
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.user_ids:
            self.get_user_list()
            if not self.user_ids:
                print("No user IDs available for updating status.")
                return

        user_id = random.choice(self.user_ids)
        status = random.choice([0, 1])  # 0 for inactive, 1 for active
        payload = {"userId": user_id, "status": status}
        with self.client.post(
            "/admin/v1/users/update-user",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to update user {user_id} status: {response.text}"
                )

    @task(10)
    def get_wallet_list(self):
        """
        GET /admin/v1/wallet/list
        Get list of wallets.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/wallet/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to get wallet list: {response.text}")

    @task(5)
    def filter_wallet_records(self):
        """
        POST /admin/v1/wallet/filter
        Filters wallet records. This endpoint expects x-www-form-urlencoded.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        # Example filters, modify as needed based on common use cases
        payload = {
            "filter_by_status": random.choice(["active", "inactive", "pending"]),
            "filter_by_date_from": "2024-01-01",
            "filter_by_date_to": "2024-01-31",
        }
        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        with self.client.post(
            "/admin/v1/wallet/filter",
            data=payload,
            headers=headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Failed to filter wallet records: {response.text}")

    @task(10)
    def get_all_admin_transactions_list(self):
        """
        GET /admin/v1/transaction/list
        Get list of all transactions from admin API.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return

        with self.client.get(
            "/admin/v1/transaction/list", headers=self.headers, catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to get all admin transactions list: {response.text}"
                )

    # Additional tasks can be added here following the same pattern
    # For example, for specific details that require IDs obtained from list calls.

    # Example of a task that requires a dynamic ID from a previous request
    # This assumes `self.football_match_ids` is populated by `get_football_match_list`
    @task(1)
    def cancel_football_match(self):
        """
        POST /admin/v1/football/cancel-match
        Cancels a football match.
        """
        if not self.auth_token:
            self.login()
            if not self.auth_token:
                return
        if not self.football_match_ids:
            self.get_football_match_list()  # Ensure match IDs are populated
            if not self.football_match_ids:
                print("No football match IDs available to cancel.")
                return

        match_id_to_cancel = random.choice(self.football_match_ids)
        payload = {
            "match_id": match_id_to_cancel,
            "is_active": 0,  # Assuming 0 means cancelled/inactive
        }
        with self.client.post(
            "/admin/v1/football/cancel-match",
            json=payload,
            headers=self.headers,
            catch_response=True,
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(
                    f"Failed to cancel football match {match_id_to_cancel}: {response.text}"
                )
