import json
import random
import os
from typing import Dict, List, Any, Optional
from locust import HttpUser, task, between
from faker import Faker
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

fake = Faker()


class SwaggerBasedApiUser(HttpUser):
    wait_time = between(1, 5)  # Users will wait between 1 and 5 seconds between tasks
    host = "https://api.credexon.com"  # Default base URL from swagger, can be overridden from command line

    # Swagger specification and generated endpoints
    swagger_spec = None
    endpoints = []
    auth_config = None

    # Store IDs fetched from API responses to reuse them (for dynamic data)
    dynamic_data = {}

    # Authentication token will be stored here
    auth_token = None
    headers = {}

    # Configuration for test data generation
    SWAGGER_FILE_PATH = "swagger.json"

    # Default login credentials (can be overridden via environment variables)
    DEFAULT_LOGIN_EMAIL = "<EMAIL>"
    DEFAULT_LOGIN_PASSWORD = "LvYFJfVw*q"

    def on_start(self):
        """
        On start of a Locust user, load swagger spec and attempt to log in.
        """
        self.load_swagger_spec()
        self.parse_swagger_endpoints()
        self.setup_authentication()
        self.login()

    def load_swagger_spec(self):
        """Load and parse the Swagger/OpenAPI specification file."""
        try:
            with open(self.SWAGGER_FILE_PATH, "r", encoding="utf-8") as f:
                self.swagger_spec = json.load(f)
            logger.info(f"Loaded Swagger spec from {self.SWAGGER_FILE_PATH}")

            # Set host from swagger if available
            if "servers" in self.swagger_spec and self.swagger_spec["servers"]:
                # Use the first server as default, prefer live server if available
                for server in self.swagger_spec["servers"]:
                    if "live" in server.get("description", "").lower():
                        self.host = server["url"]
                        break
                else:
                    self.host = self.swagger_spec["servers"][0]["url"]
                logger.info(f"Set host to: {self.host}")

        except FileNotFoundError:
            logger.error(f"Swagger file not found: {self.SWAGGER_FILE_PATH}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in swagger file: {e}")
            raise

    def setup_authentication(self):
        """Setup authentication configuration from swagger spec."""
        if not self.swagger_spec:
            return

        # Check for security schemes
        security_schemes = self.swagger_spec.get("components", {}).get(
            "securitySchemes", {}
        )
        if "bearerAuth" in security_schemes:
            self.auth_config = {
                "type": "bearer",
                "scheme": security_schemes["bearerAuth"].get("scheme", "bearer"),
                "format": security_schemes["bearerAuth"].get("bearerFormat", "JWT"),
            }
            logger.info("Found bearer authentication configuration")

    def parse_swagger_endpoints(self):
        """Parse swagger specification to extract API endpoints and their details."""
        if not self.swagger_spec or "paths" not in self.swagger_spec:
            logger.warning("No paths found in swagger specification")
            return

        self.endpoints = []
        paths = self.swagger_spec["paths"]

        for path, path_item in paths.items():
            for method, operation in path_item.items():
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    endpoint_info = {
                        "path": path,
                        "method": method.upper(),
                        "operation_id": operation.get(
                            "operationId", f"{method}_{path.replace('/', '_')}"
                        ),
                        "summary": operation.get("summary", ""),
                        "tags": operation.get("tags", []),
                        "security": operation.get("security", []),
                        "parameters": operation.get("parameters", []),
                        "request_body": operation.get("requestBody", {}),
                        "responses": operation.get("responses", {}),
                        "weight": self.calculate_endpoint_weight(
                            operation, path, method
                        ),
                    }
                    self.endpoints.append(endpoint_info)

        logger.info(
            f"Parsed {len(self.endpoints)} endpoints from swagger specification"
        )

    def calculate_endpoint_weight(self, operation: Dict, path: str, method: str) -> int:
        """Calculate weight for endpoint based on expected usage patterns."""
        # Higher weight for list/read operations
        if method.upper() == "GET":
            if "list" in path.lower() or path.endswith("/list"):
                return 10  # High weight for list endpoints
            return 5  # Medium weight for other GET endpoints

        # Medium weight for create operations
        elif method.upper() == "POST":
            if any(keyword in path.lower() for keyword in ["create", "save"]):
                return 5
            elif any(keyword in path.lower() for keyword in ["view", "filter"]):
                return 3
            return 2

        # Lower weight for update/delete operations
        elif method.upper() in ["PUT", "PATCH"]:
            return 2
        elif method.upper() == "DELETE":
            return 1

        return 1  # Default weight

    def generate_test_data_from_schema(self, schema: Dict) -> Any:
        """Generate test data based on JSON schema definition."""
        if not schema:
            return {}

        schema_type = schema.get("type", "object")

        if schema_type == "object":
            return self.generate_object_data(schema)
        elif schema_type == "array":
            return self.generate_array_data(schema)
        elif schema_type == "string":
            return self.generate_string_data(schema)
        elif schema_type == "number" or schema_type == "integer":
            return self.generate_number_data(schema)
        elif schema_type == "boolean":
            return random.choice([True, False])
        else:
            return schema.get("example", None)

    def generate_object_data(self, schema: Dict) -> Dict:
        """Generate object data from schema properties."""
        data = {}
        properties = schema.get("properties", {})
        required = schema.get("required", [])

        for prop_name, prop_schema in properties.items():
            # Always generate required fields, optionally generate others
            if prop_name in required or random.choice([True, False]):
                data[prop_name] = self.generate_test_data_from_schema(prop_schema)

        return data

    def generate_array_data(self, schema: Dict) -> List:
        """Generate array data from schema."""
        items_schema = schema.get("items", {})
        min_items = schema.get("minItems", 1)
        max_items = schema.get("maxItems", 3)

        array_length = random.randint(min_items, max_items)
        return [
            self.generate_test_data_from_schema(items_schema)
            for _ in range(array_length)
        ]

    def generate_string_data(self, schema: Dict) -> str:
        """Generate string data based on schema constraints."""
        # Check for example first
        if "example" in schema:
            return schema["example"]

        # Check for enum values
        if "enum" in schema:
            return random.choice(schema["enum"])

        # Generate based on format or pattern
        format_type = schema.get("format", "")

        if format_type == "email":
            return fake.email()
        elif format_type == "date":
            return fake.date()
        elif format_type == "date-time":
            return fake.date_time().isoformat()
        elif format_type == "uri" or format_type == "url":
            return fake.url()
        elif "password" in schema.get("description", "").lower():
            return fake.password()
        elif "phone" in schema.get("description", "").lower():
            return fake.phone_number()
        elif "name" in schema.get("description", "").lower():
            return fake.name()
        elif "title" in schema.get("description", "").lower():
            return fake.sentence(nb_words=4)
        elif "content" in schema.get("description", "").lower():
            return f"<p>{fake.text()}</p>"
        elif "slug" in schema.get("description", "").lower():
            return fake.slug()
        else:
            # Generate random string with appropriate length
            min_length = schema.get("minLength", 5)
            max_length = schema.get("maxLength", 50)
            return fake.text(max_nb_chars=random.randint(min_length, max_length))

    def generate_number_data(self, schema: Dict) -> float:
        """Generate number data based on schema constraints."""
        if "example" in schema:
            return schema["example"]

        minimum = schema.get("minimum", 1)
        maximum = schema.get("maximum", 1000)

        if schema.get("type") == "integer":
            return random.randint(int(minimum), int(maximum))
        else:
            return round(random.uniform(minimum, maximum), 2)

    def get_dynamic_value(self, param_name: str, param_type: str = "string") -> Any:
        """Get dynamic value from previously stored data or generate new one."""
        # Map common parameter names to stored data
        id_mappings = {
            "banner_id": "banner_ids",
            "category_name": "category_names",
            "cms_id": "cms_ids",
            "contest_id": "contest_ids",
            "match_id": "match_ids",
            "user_id": "user_ids",
            "pool_id": "pool_ids",
        }

        if param_name in id_mappings:
            data_key = id_mappings[param_name]
            if data_key in self.dynamic_data and self.dynamic_data[data_key]:
                return random.choice(self.dynamic_data[data_key])

        # Generate fallback data based on type
        if param_type == "integer":
            return random.randint(1, 1000)
        elif param_type == "number":
            return round(random.uniform(1, 1000), 2)
        else:
            return fake.word()

    def store_dynamic_data(self, response_data: Dict, endpoint_path: str):
        """Store IDs and other useful data from API responses for future use."""
        if not response_data:
            return

        # Common patterns for extracting IDs from responses
        data_section = response_data.get("data", {})

        # Extract various types of IDs
        if isinstance(data_section, dict):
            # Single item responses
            for key, value in data_section.items():
                if key.endswith("_id") and value:
                    list_key = key.replace("_id", "_ids")
                    if list_key not in self.dynamic_data:
                        self.dynamic_data[list_key] = []
                    if value not in self.dynamic_data[list_key]:
                        self.dynamic_data[list_key].append(value)

            # List responses
            for list_key in [
                "banner_list",
                "category_list",
                "cms_list",
                "contest_list",
                "match_list",
                "user_list",
            ]:
                if list_key in data_section:
                    items = data_section[list_key]
                    if isinstance(items, list):
                        id_key = list_key.replace("_list", "_ids")
                        if id_key not in self.dynamic_data:
                            self.dynamic_data[id_key] = []

                        for item in items:
                            if isinstance(item, dict):
                                # Try different ID field names
                                for id_field in [
                                    "_id",
                                    "id",
                                    f"{list_key.split('_')[0]}_id",
                                ]:
                                    if id_field in item and item[id_field]:
                                        if (
                                            item[id_field]
                                            not in self.dynamic_data[id_key]
                                        ):
                                            self.dynamic_data[id_key].append(
                                                item[id_field]
                                            )
                                        break

    def login(self):
        """
        Handles the admin user login to obtain a bearer token.
        Uses credentials from environment variables or defaults.
        """
        login_endpoint = "/admin/v1/vendor/login"
        login_payload = {
            "email": os.getenv("LOGIN_EMAIL", self.DEFAULT_LOGIN_EMAIL),
            "password": os.getenv("LOGIN_PASSWORD", self.DEFAULT_LOGIN_PASSWORD),
        }
        with self.client.post(
            login_endpoint, json=login_payload, catch_response=True
        ) as response:
            if response.status_code == 200:
                response_data = response.json()
                token_path = response_data.get("data", {}).get("user", {}).get("token")
                if token_path:
                    self.auth_token = token_path
                    self.headers = {"Authorization": f"Bearer {self.auth_token}"}
                    response.success()
                    logger.info(
                        f"Successfully logged in, token: {self.auth_token[:10]}..."
                    )
                else:
                    response.failure("Login successful but no token received")
            else:
                response.failure(
                    f"Login failed with status code {response.status_code}: {response.text}"
                )

    def create_dynamic_task(self, endpoint: Dict):
        """Create a dynamic task function for the given endpoint."""

        def dynamic_task_func(self):
            return self.execute_endpoint_request(endpoint)

        # Set task attributes
        dynamic_task_func.__name__ = f"task_{endpoint['operation_id']}"
        dynamic_task_func.__doc__ = (
            f"{endpoint['method']} {endpoint['path']} - {endpoint['summary']}"
        )

        # Apply task decorator with weight
        return task(endpoint["weight"])(dynamic_task_func)

    def execute_endpoint_request(self, endpoint: Dict):
        """Execute a request for the given endpoint with generated test data."""
        # Check if authentication is required
        if endpoint["security"] and not self.auth_token:
            self.login()
            if not self.auth_token:
                logger.warning(
                    f"Skipping {endpoint['path']} - authentication required but failed"
                )
                return

        # Prepare request parameters
        url = self.prepare_url(endpoint)
        headers = self.prepare_headers(endpoint)
        data = self.prepare_request_data(endpoint)

        # Execute request
        method = endpoint["method"].lower()
        request_kwargs = {"url": url, "headers": headers, "catch_response": True}

        # Add data based on method and content type
        if data and method in ["post", "put", "patch"]:
            if headers.get("Content-Type") == "application/x-www-form-urlencoded":
                request_kwargs["data"] = data
            else:
                request_kwargs["json"] = data

        # Make the request
        with getattr(self.client, method)(**request_kwargs) as response:
            self.handle_response(response, endpoint, data)

    def prepare_url(self, endpoint: Dict) -> str:
        """Prepare URL with path parameters."""
        url = endpoint["path"]

        # Replace path parameters with dynamic values
        for param in endpoint["parameters"]:
            if param.get("in") == "path":
                param_name = param["name"]
                param_schema = param.get("schema", {})
                param_type = param_schema.get("type", "string")

                # Get dynamic value or generate one
                value = self.get_dynamic_value(param_name, param_type)
                url = url.replace(f"{{{param_name}}}", str(value))

        return url

    def prepare_headers(self, endpoint: Dict) -> Dict:
        """Prepare request headers including authentication."""
        headers = self.headers.copy()

        # Add headers from parameters
        for param in endpoint["parameters"]:
            if param.get("in") == "header":
                param_name = param["name"]
                param_schema = param.get("schema", {})
                headers[param_name] = self.generate_test_data_from_schema(param_schema)

        # Set content type for request body
        if endpoint["request_body"]:
            content_types = list(endpoint["request_body"].get("content", {}).keys())
            if content_types:
                headers["Content-Type"] = content_types[0]

        return headers

    def prepare_request_data(self, endpoint: Dict) -> Optional[Dict]:
        """Prepare request body data."""
        if not endpoint["request_body"]:
            return None

        content = endpoint["request_body"].get("content", {})

        # Handle different content types
        for content_type, content_schema in content.items():
            schema = content_schema.get("schema", {})

            if content_type == "application/json":
                return self.generate_test_data_from_schema(schema)
            elif content_type == "application/x-www-form-urlencoded":
                # Generate form data
                return self.generate_test_data_from_schema(schema)

        return None

    def handle_response(self, response, endpoint: Dict, request_data: Dict):
        """Handle API response and extract useful data."""
        endpoint_name = f"{endpoint['method']} {endpoint['path']}"

        if response.status_code in [200, 201, 202]:
            response.success()

            # Try to extract and store dynamic data
            try:
                response_data = response.json()
                self.store_dynamic_data(response_data, endpoint["path"])
                logger.debug(f"✓ {endpoint_name} - Success")
            except (ValueError, json.JSONDecodeError):
                # Response might not be JSON
                logger.debug(f"✓ {endpoint_name} - Success (non-JSON response)")

        elif response.status_code == 401:
            # Authentication failed, try to re-login
            logger.warning(
                f"Authentication failed for {endpoint_name}, attempting re-login"
            )
            self.login()
            response.failure(f"Authentication failed: {response.text}")

        elif response.status_code in [400, 422]:
            # Validation error - this is expected with random data
            logger.debug(
                f"⚠ {endpoint_name} - Validation error (expected with random data)"
            )
            response.failure(f"Validation error: {response.text}")

        else:
            # Other errors
            logger.warning(
                f"✗ {endpoint_name} - Error {response.status_code}: {response.text}"
            )
            response.failure(
                f"Request failed with status {response.status_code}: {response.text}"
            )


# Dynamically create task methods from swagger endpoints
def create_user_class_with_dynamic_tasks():
    """Create the user class with dynamically generated tasks from swagger spec."""

    # Load swagger spec to generate tasks
    try:
        with open("swagger.json", "r", encoding="utf-8") as f:
            swagger_spec = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Failed to load swagger spec for task generation: {e}")
        # Return the class without dynamic tasks
        return SwaggerBasedApiUser

    # Create a new class that inherits from SwaggerBasedApiUser
    class DynamicSwaggerApiUser(SwaggerBasedApiUser):
        pass

    # Parse endpoints and create tasks
    if "paths" in swagger_spec:
        task_count = 0
        for path, path_item in swagger_spec["paths"].items():
            for method, operation in path_item.items():
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    endpoint_info = {
                        "path": path,
                        "method": method.upper(),
                        "operation_id": operation.get(
                            "operationId",
                            f"{method}_{path.replace('/', '_').replace('{', '').replace('}', '')}",
                        ),
                        "summary": operation.get("summary", ""),
                        "tags": operation.get("tags", []),
                        "security": operation.get("security", []),
                        "parameters": operation.get("parameters", []),
                        "request_body": operation.get("requestBody", {}),
                        "responses": operation.get("responses", {}),
                        "weight": calculate_endpoint_weight_static(
                            operation, path, method
                        ),
                    }

                    # Create and add the task method
                    task_method = create_task_method(endpoint_info)
                    setattr(
                        DynamicSwaggerApiUser,
                        f"task_{endpoint_info['operation_id']}",
                        task_method,
                    )
                    task_count += 1

        logger.info(f"Created {task_count} dynamic tasks from swagger specification")

    return DynamicSwaggerApiUser


def calculate_endpoint_weight_static(operation: Dict, path: str, method: str) -> int:
    """Static version of weight calculation for class creation."""
    if method.upper() == "GET":
        if "list" in path.lower() or path.endswith("/list"):
            return 10
        return 5
    elif method.upper() == "POST":
        if any(keyword in path.lower() for keyword in ["create", "save"]):
            return 5
        elif any(keyword in path.lower() for keyword in ["view", "filter"]):
            return 3
        return 2
    elif method.upper() in ["PUT", "PATCH"]:
        return 2
    elif method.upper() == "DELETE":
        return 1
    return 1


def create_task_method(endpoint_info: Dict):
    """Create a task method for the given endpoint."""

    def task_method(self):
        return self.execute_endpoint_request(endpoint_info)

    # Set method attributes
    task_method.__name__ = f"task_{endpoint_info['operation_id']}"
    task_method.__doc__ = f"{endpoint_info['method']} {endpoint_info['path']} - {endpoint_info['summary']}"

    # Apply task decorator with weight
    return task(endpoint_info["weight"])(task_method)


# Create the final user class with dynamic tasks
SwaggerApiUser = create_user_class_with_dynamic_tasks()


# Legacy class for backward compatibility (if needed)
class AdminApiUser(SwaggerBasedApiUser):
    """Legacy class name for backward compatibility."""

    pass
