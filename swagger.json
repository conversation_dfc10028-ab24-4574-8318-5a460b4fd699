{"openapi": "3.0.0", "info": {"title": "Credexon B2B API", "version": "1.0.0", "description": "API documentation for Credexon B2B backend"}, "servers": [{"url": "http://localhost:8881"}, {"url": "https://preapi.credexon.com", "description": "live server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "Schemas": {"CreateCMS": {"type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "example": "About Us"}, "content": {"type": "string", "example": "<p>This is the about us content</p>"}, "slug": {"type": "string", "example": "about-us"}}}, "UpdateCMS": {"type": "object", "required": ["id", "title"], "properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Updated About Us"}, "content": {"type": "string", "example": "<p>Updated content</p>"}, "slug": {"type": "string", "example": "about-us"}}}, "ViewCMS": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "example": 1}}}, "PlayerListRequest": {"type": "object", "required": ["match_id"], "properties": {"match_id": {"type": "integer", "example": 101}}}}}, "security": [{"bearerAuth": []}], "paths": {"/admin/v1/banner/save": {"post": {"summary": "Create a new banner", "tags": ["Banner"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "example": "homepage"}, "image": {"type": "string", "example": "https://example.com/banner.jpg"}, "sequence": {"type": "number", "example": 1}, "status": {"type": "number", "example": 1}, "banner_link": {"type": "string", "example": "https://example.com/target-page"}}, "required": ["type", "image", "sequence", "status", "banner_link"]}}}}, "responses": {"200": {"description": "Banner created successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/banner/list": {"get": {"summary": "Get list of banners", "tags": ["Banner"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of banners"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/banner/edit": {"post": {"summary": "Edit an existing banner", "tags": ["Banner"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"banner_id": {"type": "string", "example": "64b3e0a5e45d10001c0def12"}, "type": {"type": "string", "example": "homepage"}, "image": {"type": "string", "example": "https://example.com/banner-updated.jpg"}, "sequence": {"type": "number", "example": 2}, "device": {"type": "string", "example": "mobile"}, "status": {"type": "number", "example": 1}, "banner_link": {"type": "string", "example": "https://example.com/updated-link"}}, "required": ["banner_id"]}}}}, "responses": {"200": {"description": "Banner updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/category/create": {"post": {"summary": "Create or update a category", "tags": ["Category"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Electronics"}, "description": {"type": "string", "example": "Category for electronic items"}, "image": {"type": "string", "example": "https://example.com/image.jpg"}, "status": {"type": "number", "example": 1}}, "required": ["name"]}}}}, "responses": {"200": {"description": "Category created or updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/category/list": {"get": {"summary": "Get list of categories", "tags": ["Category"], "responses": {"200": {"description": "List of categories"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/cms/list": {"get": {"summary": "Get all CMS entries", "tags": ["CMS"], "responses": {"200": {"description": "List of CMS pages"}}}}, "/admin/v1/cms/view": {"post": {"summary": "View a specific CMS entry", "tags": ["CMS"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["cms_id"], "properties": {"cms_id": {"type": "integer", "description": "The ID of the CMS entry to view", "example": 1}}}}}}, "responses": {"200": {"description": "CMS entry found"}, "404": {"description": "CMS not found"}}}}, "/admin/v1/cms/update": {"put": {"summary": "Update a CMS entry", "tags": ["CMS"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "example": "Terms and Conditions"}, "slug": {"type": "string", "example": "terms-and-conditions"}, "content": {"type": "string", "example": "<p>Updated terms and conditions content.</p>"}, "cms_id": {"type": "string", "example": "64b4c2a3f5a1d0001cfabcd1"}}, "required": ["title", "slug", "content", "cms_id"]}}}}, "responses": {"200": {"description": "CMS entry updated"}, "400": {"description": "Validation error"}}}}, "/admin/v1/cms/create": {"post": {"summary": "Create a new CMS entry", "tags": ["CMS"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "example": "About Us"}, "slug": {"type": "string", "example": "about-us"}, "content": {"type": "string", "example": "<p>Welcome to our website!</p>"}}, "required": ["title", "slug", "content"]}}}}, "responses": {"200": {"description": "CMS entry created"}, "400": {"description": "Validation error"}}}}, "/admin/v1/contactus/get_all_contact_us_requests": {"get": {"summary": "Retrieve all contact us requests", "tags": ["ContactUs"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "A list of contact us messages"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/contest/create": {"post": {"summary": "Create a new contest", "tags": ["Contests"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "title", "type": "string", "required": true, "description": "Title of the contest"}, {"in": "formData", "name": "subtitle", "type": "string", "required": true, "description": "Subtitle of the contest"}, {"in": "formData", "name": "dis_val", "type": "number", "required": true, "description": "Discount value or points"}], "responses": {"200": {"description": "Contest created successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/contest/view": {"post": {"summary": "View a specific contest", "tags": ["Contests"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "648f8c9e8f4b9a2b12345678"}}}}}}, "responses": {"200": {"description": "Contest details retrieved successfully"}}}}, "/admin/v1/contest/update": {"post": {"summary": "Update an existing contest", "tags": ["Contests"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "648f8c9e8f4b9a2b12345678"}, "title": {"type": "string", "example": "Updated Contest Title"}, "subtitle": {"type": "string", "example": "Updated Contest Subtitle"}, "contestlogo": {"type": "string", "example": "https://example.com/logo.png"}, "dis_val": {"type": "number", "example": 15}}}}}}, "responses": {"200": {"description": "Contest updated successfully"}}}}, "/admin/v1/contest/active-inactive": {"post": {"summary": "Toggle contest active status", "tags": ["Contests"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "648f8c9e8f4b9a2b12345678"}, "status": {"type": "number", "example": 1}}, "required": ["contest_id", "status"]}}}}, "responses": {"200": {"description": "Contest status updated"}}}}, "/admin/v1/contest/change-contest-order": {"post": {"summary": "Change contest display order", "tags": ["Contests"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContestOrder"}}}}, "responses": {"200": {"description": "Contest order changed successfully"}}}}, "/admin/v1/contest/list": {"get": {"summary": "List all contests", "tags": ["Contests"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of contests"}}}}, "/admin/v1/dashboard/list": {"get": {"summary": "Get dashboard list", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dashboard list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/dashboard/dashboard_vender_details": {"post": {"summary": "Get vendor details for dashboard", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "64a1e2c45f2e3a001234abcd"}}, "required": ["id"]}}}}, "responses": {"200": {"description": "Vendor details retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/football/list": {"get": {"summary": "Get list of football matches", "tags": ["Football"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Football match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/football/football_activity_list": {"get": {"summary": "Get list of football match activities", "tags": ["Football"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Football activity list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/football/active-inactive": {"post": {"summary": "Activate or deactivate a football match", "tags": ["Football"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_active": {"type": "number", "example": 1}}, "required": ["match_id", "is_active"]}}}}, "responses": {"200": {"description": "Match activation status updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/football/cancel-match": {"post": {"summary": "Cancel a football match", "tags": ["Football"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_active": {"type": "number", "example": 0}}, "required": ["match_id", "is_active"]}}}}, "responses": {"200": {"description": "Match cancelled successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/football/active_list": {"get": {"summary": "Get list of active football matches", "tags": ["Football"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active football match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/football/publish": {"post": {"summary": "Publish or unpublish a football match", "tags": ["Football"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_publish": {"type": "number", "example": 1}}, "required": ["match_id", "is_publish"]}}}}, "responses": {"200": {"description": "Football match publish status updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/accumulator/match_list": {"post": {"summary": "Get player list by match", "tags": ["MatchAccumulator"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "example": "T20"}, "match_id": {"type": "number", "example": 101}}, "required": ["type", "match_id"]}}}}, "responses": {"200": {"description": "Player list fetched successfully"}}}}, "/admin/v1/match/list": {"get": {"summary": "Get match list", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/cricket_activity_list": {"get": {"summary": "Get cricket activity list", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Cricket activity list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/listByStatus": {"get": {"summary": "Get match list filtered by status", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Match list filtered by status retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/footballlistByStatus": {"get": {"summary": "Get football match list filtered by status", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Football match list filtered by status retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/series/list": {"get": {"summary": "Get list of series matches", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Series match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/series/active_list": {"get": {"summary": "Get active series list", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active series list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/series/active-inactive": {"post": {"summary": "Activate or deactivate a series (Cricket)", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Series status updated successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/series/football/active_inactive": {"post": {"summary": "Activate or deactivate a football series", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Football series status updated successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/active-inactive": {"post": {"summary": "Activate or deactivate a match", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_active": {"type": "number", "description": "1 for active, 0 for inactive", "example": 1}}, "required": ["match_id", "is_active"]}}}}, "responses": {"200": {"description": "Match status updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/cancel-match": {"post": {"summary": "Cancel a match", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_active": {"type": "number", "description": "1 for active, 0 for inactive", "example": 0}}, "required": ["match_id", "is_active"]}}}}, "responses": {"200": {"description": "Match cancelled successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/active_list": {"get": {"summary": "Get active matches list", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active matches list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/publish": {"post": {"summary": "Publish or unpublish a match", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 101}, "is_publish": {"type": "number", "description": "1 to publish, 0 to unpublish", "example": 1}}, "required": ["match_id", "is_publish"]}}}}, "responses": {"200": {"description": "Match publish status updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/fantasy_list": {"get": {"summary": "Get fantasy match list", "tags": ["Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Fantasy match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/fantasygame_list": {"post": {"summary": "Get fantasy game list by game ID", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"game_id": {"type": "number", "example": 123}}, "required": ["game_id"]}}}}, "responses": {"200": {"description": "Fantasy game list retrieved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/fantasygame_type_list": {"post": {"summary": "Get fantasy game type list by game ID and type", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"game_id": {"type": "number", "example": 123}, "type": {"type": "string", "example": "premium"}}, "required": ["game_id", "type"]}}}}, "responses": {"200": {"description": "Fantasy game type list retrieved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/update_fantasygame_points": {"put": {"summary": "Update fantasy game points", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"game_id": {"type": "number", "example": 123}, "type": {"type": "string", "example": "premium"}, "points": {"type": "object", "example": {"player1": 10, "player2": 15}}}, "required": ["game_id", "type", "points"]}}}}, "responses": {"200": {"description": "Fantasy game points updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/match/current_match_list": {"post": {"summary": "Get current match list", "tags": ["Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Current match list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/notification/send_message": {"post": {"summary": "Send a notification message", "tags": ["Notification"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userids": {"type": "array", "items": {"type": "number"}, "example": [1, 2, 3]}, "enddate": {"type": "string", "format": "date", "nullable": true, "example": "2025-07-31"}, "isexpire": {"type": "number", "example": 1}, "type": {"type": "string", "example": "info"}, "body": {"type": "string", "example": "Your match starts in 1 hour!"}, "isadmin": {"type": "number", "example": 1}, "title": {"type": "string", "example": "Match Reminder"}}, "required": ["userids", "isexpire", "type", "body", "title"]}}}}, "responses": {"200": {"description": "Notification sent successfully"}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/notification/list": {"post": {"summary": "Get list of notifications", "tags": ["Notification"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Notification list retrieved successfully"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/playeraccumulator/series_list": {"get": {"summary": "Get list of series for player accumulator", "tags": ["Player Accumulator"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of series retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/playeraccumulator/match_list": {"post": {"summary": "Get list of matches based on series", "tags": ["Player Accumulator"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"series_id": {"type": "integer", "example": 101}}}}}}, "responses": {"200": {"description": "List of matches"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/playeraccumulator/player_list": {"post": {"summary": "Get list of players for selected match", "tags": ["Player Accumulator"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "integer", "example": 501}}}}}}, "responses": {"200": {"description": "List of players"}}}}, "/admin/v1/playeraccumulator/update_player": {"post": {"summary": "Update player accumulator details", "tags": ["Player Accumulator"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"player_id": {"type": "integer", "example": 1}, "accumulator_score": {"type": "number", "example": 35.5}}}}}}, "responses": {"200": {"description": "Player data updated"}}}}, "/admin/v1/plymetadata/ply_add": {"post": {"summary": "Add or update player metadata", "tags": ["Player"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "type", "type": "string", "required": true, "description": "Type of player metadata"}, {"in": "formData", "name": "pid", "type": "integer", "required": true, "description": "Player ID"}, {"in": "formData", "name": "logo_url", "type": "string", "required": false, "description": "URL of player logo"}, {"in": "formData", "name": "jersy_no", "type": "integer", "required": false, "description": "Jersey number"}], "responses": {"200": {"description": "Player metadata added or updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/plymetadata/cricket/status": {"post": {"summary": "Update cricket player status", "tags": ["Player"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "pid", "type": "integer", "required": true, "description": "Player ID"}, {"in": "formData", "name": "status", "type": "integer", "required": true, "description": "New status value"}], "responses": {"200": {"description": "Player status updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/plymetadata/football/status": {"post": {"summary": "Update football player status", "tags": ["Player"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "pid", "type": "integer", "required": true, "description": "Player ID"}, {"in": "formData", "name": "status", "type": "integer", "required": true, "description": "New status value"}], "responses": {"200": {"description": "Player status updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/plymetadata/player_list": {"post": {"summary": "Get list of players", "tags": ["Player"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Player list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/plymetadata/sereis_player_list": {"post": {"summary": "Get list of players for a series", "tags": ["Match"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "type", "type": "string", "required": true, "description": "Type of the request"}, {"in": "formData", "name": "league_id", "type": "integer", "required": true, "description": "League ID"}, {"in": "formData", "name": "contest_id", "type": "string", "required": false, "description": "Optional contest ID"}], "responses": {"200": {"description": "Series player list retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/poolmaster/create": {"post": {"summary": "Create a new pool", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "description": "Pool creation data (fields not specified)"}}}}, "responses": {"200": {"description": "Pool created successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/pool_calculation": {"post": {"summary": "Perform pool calculation", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "description": "Pool calculation data (fields not specified)"}}}}, "responses": {"200": {"description": "Pool calculation completed successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/series_datetime_pool": {"post": {"summary": "Save series pool date/time information", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "CT2025"}, "date_start": {"type": "string", "example": "2025-06-01"}, "date_end": {"type": "string", "example": "2025-06-10"}, "matchid_start": {"type": "number", "example": 101}, "matchid_end": {"type": "number", "example": 110}, "gtype": {"type": "string", "example": "cricket"}, "league_id": {"type": "number", "example": 5}, "session_id": {"type": "number", "example": 2}}, "required": ["contest_id", "date_start", "date_end", "matchid_start", "matchid_end", "gtype", "league_id", "session_id"]}}}}, "responses": {"200": {"description": "Series pool date saved successfully"}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/pool_range": {"post": {"summary": "Create prize range for a pool", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"poolmaster_id": {"type": "string", "example": "PM001"}, "pmin": {"type": "number", "example": 1}, "pmax": {"type": "number", "example": 10}, "pamount": {"type": "number", "example": 500}}, "required": ["poolmaster_id", "pmin", "pmax", "pamount"]}}}}, "responses": {"200": {"description": "Pool prize range created successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/cricket/active_pool_list": {"post": {"summary": "Get active cricket pool contest list for a match", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Active pool contest list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/cricket/active_contest_pool_status_list": {"post": {"summary": "Get active cricket contest pool status list for a match", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Active contest pool status list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/cricket/active_all_contest_pool_status_list": {"post": {"summary": "Get all active cricket contest pool status list", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "All active contest pool status list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/all_active_pool_list": {"post": {"summary": "Get list of all active contest pools", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Active contest pools retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/football/active_pool_list": {"post": {"summary": "Get active football pool contest list", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "example": 202}, "type": {"type": "string", "example": "standard"}, "countrytype": {"type": "string", "example": "international"}, "gtype": {"type": "string", "example": "football"}, "contest_id": {"type": "string", "example": "FT123"}}, "required": []}}}}, "responses": {"200": {"description": "Active football pool contests retrieved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/edit_pool_range": {"post": {"summary": "Edit prize range for a pool", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"poolprizebreak_id": {"type": "string", "example": "PPB001"}, "pmin": {"type": "number", "example": 1}, "pmax": {"type": "number", "example": 5}, "pamount": {"type": "number", "example": 1000}}, "required": ["poolprizebreak_id", "pmin", "pmax", "pamount"]}}}}, "responses": {"200": {"description": "Pool prize range updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/active_inactive_pool": {"post": {"summary": "Toggle active/inactive status of a pool", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Pool status updated successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/active_inactive_contest_pool_match": {"post": {"summary": "Toggle active/inactive status of contest pool for a match", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "No parameters required"}}}}, "responses": {"200": {"description": "Contest pool match status updated successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/delete": {"delete": {"summary": "Delete a pool prize break entry", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"poolprizebreak_id": {"type": "string", "example": "PPB001"}}, "required": ["poolprizebreak_id"]}}}}, "responses": {"200": {"description": "Pool prize break deleted successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/list": {"post": {"summary": "Get list of pool masters based on contest and game type", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "C123"}, "gtype": {"type": "string", "example": "cricket"}, "type": {"type": "string", "example": "standard"}}, "required": ["contest_id", "gtype", "type"]}}}}, "responses": {"200": {"description": "Pool master list retrieved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/view": {"post": {"summary": "View detailed pool list for a contest", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_id": {"type": "string", "example": "C123"}}, "required": ["contest_id"]}}}}, "responses": {"200": {"description": "Pool list view retrieved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/poolmaster/ischecked": {"post": {"summary": "Mark a pool as checked or unchecked", "tags": ["Pool Master"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"pool_id": {"type": "string", "example": "POOL123"}, "isChecked": {"type": "number", "example": 1}}, "required": ["pool_id", "isChecked"]}}}}, "responses": {"200": {"description": "Pool check status updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/report/get_match_list": {"post": {"summary": "Get list of matches for reporting", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Match list retrieved successfully"}}}}, "/admin/v1/report/get_report_data": {"post": {"summary": "Get report data", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Report data fetched successfully"}}}}, "/admin/v1/report/get_report_download": {"post": {"summary": "Download report data", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Report download initiated successfully"}}}}, "/admin/v1/report/get-downloads": {"get": {"summary": "Get list of downloadable files", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of downloadable files"}}}}, "/admin/v1/report/download-file/{fileId}": {"get": {"summary": "Download a specific file by ID", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID of the file to download"}], "responses": {"200": {"description": "File downloaded successfully"}, "404": {"description": "File not found"}}}}, "/admin/v1/report/files": {"delete": {"summary": "Delete all report files", "tags": ["ReportManagement"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All files deleted successfully"}}}}, "/admin/v1/report/get_download_affiliates": {"get": {"summary": "Get affiliate download tracking data", "tags": ["ReportManagement"], "responses": {"200": {"description": "Affiliate download tracking data fetched"}}}}, "/admin/v1/report/mongo_export_csv": {"get": {"summary": "Export MongoDB data to CSV", "tags": ["ReportManagement"], "responses": {"200": {"description": "MongoDB data exported successfully"}}}}, "/admin/v1/report/update_user": {"get": {"summary": "Update user information", "tags": ["ReportManagement"], "responses": {"200": {"description": "User updated successfully"}}}}, "/admin/v1/series/ckt_team": {"post": {"summary": "Find cricket team by name", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": false, "description": "Name of the cricket team"}], "responses": {"200": {"description": "Cricket team found successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/edit_ckt": {"post": {"summary": "Edit cricket team details", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "team_id", "type": "string", "required": true, "description": "ID of the team to edit"}, {"in": "formData", "name": "logo_url", "type": "string", "required": false, "description": "URL of the team's logo"}, {"in": "formData", "name": "short_name", "type": "string", "required": false, "description": "Short name of the team"}], "responses": {"200": {"description": "Cricket team edited successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/fb_team_list_old": {"get": {"summary": "Get list of old football teams", "tags": ["SeriesManager"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Football team list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/fb_team": {"post": {"summary": "Find football team by name", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": false, "description": "Name of the football team"}], "responses": {"200": {"description": "Football team found successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/edit_fb": {"post": {"summary": "Edit football team details", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "team_id", "type": "string", "required": true, "description": "ID of the team to edit"}, {"in": "formData", "name": "logo_path", "type": "string", "required": false, "description": "Path to the team's logo"}, {"in": "formData", "name": "short_code", "type": "string", "required": false, "description": "Short code of the team"}], "responses": {"200": {"description": "Football team edited successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/series_team_add": {"post": {"summary": "Add or update series team metadata", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "type", "type": "string", "required": true, "description": "Type of operation"}, {"in": "formData", "name": "league_id", "type": "integer", "required": true, "description": "League ID"}, {"in": "formData", "name": "logo_url", "type": "string", "required": false, "description": "URL of the team's logo"}, {"in": "formData", "name": "short_name", "type": "string", "required": false, "description": "Short name of the team"}], "responses": {"200": {"description": "Series team metadata added or updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/ckt_team_list": {"post": {"summary": "Get list of cricket teams", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": false, "description": "Filter by team name"}], "responses": {"200": {"description": "Cricket team list retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/series/fb_team_list": {"post": {"summary": "Get list of football teams", "tags": ["SeriesManager"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": false, "description": "Filter by team name"}], "responses": {"200": {"description": "Football team list retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/setting/create": {"post": {"summary": "Create or update settings", "tags": ["Settings"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCMS"}}}}, "responses": {"200": {"description": "Setting<PERSON> saved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/setting/view": {"get": {"summary": "View settings", "tags": ["Settings"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Settings retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/statemanger/list": {"get": {"summary": "Get the list of states", "tags": ["StateManager"], "responses": {"200": {"description": "List of states retrieved successfully"}}}}, "/admin/v1/statemanger/edit": {"post": {"summary": "Edit a state", "tags": ["StateManager"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "example": "64b2fbd05f3e80001c0abcde"}, "status": {"type": "number", "example": 1}}, "required": ["id", "status"]}}}}, "responses": {"200": {"description": "State updated successfully"}, "400": {"description": "Validation error"}}}}, "/admin/v1/subadmin/create": {"post": {"summary": "Create a new subadmin user", "tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Full name of the user"}, "email": {"type": "string", "description": "Email address of the user"}, "usertype": {"type": "number", "description": "User type ID"}, "country_code": {"type": "string", "description": "Country code"}, "phone": {"type": "string", "description": "Phone number"}, "password": {"type": "string", "description": "User password"}, "dob": {"type": "string", "description": "Date of birth", "example": "1990-01-01"}, "gender": {"type": "string", "description": "Gender", "example": "Male"}, "module_data": {"type": "object", "description": "JSON object with module permissions", "example": {"User": true, "Cms_Manager": false, "Matches": true}}}, "required": ["name", "email", "usertype", "country_code", "phone", "password"]}}}}, "responses": {"200": {"description": "Subadmin created successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/subadmin/list": {"post": {"summary": "Get a list of all subadmin users", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subadmin list retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/subadmin/active-inactive": {"post": {"summary": "Toggle active/inactive status of a subadmin user", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subadmin status updated successfully"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/subadmin/delete": {"delete": {"summary": "Delete a subadmin user", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subadmin deleted successfully"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Subadmin not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/subadmin/update": {"post": {"summary": "Update a subadmin user", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subadmin updated successfully"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Subadmin not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/subadmin/view": {"post": {"summary": "View details of a subadmin user", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subadmin details retrieved successfully"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Subadmin not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/tds/list": {"post": {"summary": "Get list of TDS records", "tags": ["TDS"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "TDS list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/transaction/list": {"get": {"summary": "Get list of transactions", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of transactions retrieved successfully"}}}}, "/admin/v1/transaction/filter": {"post": {"summary": "Filter transactions", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date", "example": "2024-01-01"}, "endDate": {"type": "string", "format": "date", "example": "2024-01-31"}, "status": {"type": "string", "example": "completed"}}}}}}, "responses": {"200": {"description": "Filtered transactions retrieved successfully"}}}}, "/admin/v1/transaction/download": {"post": {"summary": "Download transaction sheet", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Transaction sheet downloaded successfully"}}}}, "/admin/v1/transaction/get-downloads": {"get": {"summary": "Get downloadable transaction files", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of downloadable files"}}}}, "/admin/v1/transaction/download-file/{fileId}": {"get": {"summary": "Download a specific transaction file by ID", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID of the file to download"}], "responses": {"200": {"description": "File downloaded successfully"}, "404": {"description": "File not found"}}}}, "/admin/v1/transaction/files": {"delete": {"summary": "Delete all transaction files", "tags": ["Transactions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "All transaction files deleted successfully"}}}}, "/admin/v1/users/create": {"post": {"summary": "Create a new user", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "usertype": {"type": "number", "example": 1}, "country_code": {"type": "string", "example": "+1"}, "phone": {"type": "string", "example": "1234567890"}, "password": {"type": "string", "example": "SecurePass123!"}, "logintype": {"type": "string", "example": "manual"}, "profilepic": {"type": "string", "example": "https://example.com/profile.jpg"}}, "required": ["name", "email", "usertype", "country_code", "phone", "password", "logintype"]}}}}, "responses": {"201": {"description": "User created successfully"}, "400": {"description": "Validation error"}, "409": {"description": "User already exists"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/login": {"post": {"summary": "Authenticate a user (login)", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "UserPass123!"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful, returns user data and token"}, "400": {"description": "Missing or invalid email/password"}, "401": {"description": "Authentication failed"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/logout": {"get": {"summary": "Logout the authenticated user", "tags": ["<PERSON><PERSON>"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Logout successful"}, "401": {"description": "Unauthorized - token missing or invalid"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/forgot": {"post": {"summary": "Initiate forgot password process", "tags": ["<PERSON><PERSON>"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"phone": {"type": "string", "example": "1234567890"}}, "required": ["phone"]}}}}, "responses": {"200": {"description": "Password reset process initiated"}, "400": {"description": "Invalid or missing phone number"}, "401": {"description": "Unauthorized"}, "404": {"description": "User with provided phone not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/list": {"get": {"summary": "Get list of users", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of users retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/uservendorList": {"post": {"summary": "Get list of user vendors", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of user vendors retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/userList": {"post": {"summary": "Get list of users", "tags": ["User"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of users retrieved successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/bank_list": {"get": {"summary": "Get list of banks", "tags": ["Bank"], "responses": {"200": {"description": "List of banks retrieved successfully"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/bank_verified": {"post": {"summary": "Verify or reject a user's bank information", "tags": ["Bank"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userid": {"type": "number", "example": 101}, "isverified": {"type": "number", "description": "1 for verified, 0 for rejected", "example": 1}, "reject_reason": {"type": "string", "description": "Reason for rejection (if any)", "example": "Incorrect account details"}}, "required": ["userid", "isverified"]}}}}, "responses": {"200": {"description": "Bank verification status updated successfully"}, "400": {"description": "Invalid input"}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/update-user": {"post": {"summary": "Update a user's status", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "number", "example": 123}, "status": {"type": "number", "example": 1}}, "required": ["userId", "status"]}}}}, "responses": {"200": {"description": "User updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/delete": {"delete": {"summary": "Delete a user", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "number", "example": 101}}, "required": ["user_id"]}}}}, "responses": {"200": {"description": "User deleted successfully"}, "400": {"description": "Invalid user ID"}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/view": {"post": {"summary": "View user details", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number", "example": 101}}, "required": ["id"]}}}}, "responses": {"200": {"description": "User details retrieved successfully"}, "400": {"description": "Invalid ID"}, "401": {"description": "Unauthorized"}, "404": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/admin/v1/users/payment_access": {"post": {"summary": "Get payment access", "tags": ["Payments"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}}, "required": ["user_id"]}}}}, "responses": {"200": {"description": "Payment access details retrieved successfully"}, "400": {"description": "Bad request"}}}}, "/admin/v1/users/transaction_list": {"post": {"summary": "Get transaction list", "tags": ["Transactions"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}}, "required": ["user_id"]}}}}, "responses": {"200": {"description": "Transaction list fetched successfully"}, "400": {"description": "Bad request"}}}}, "/admin/v1/users/change_password": {"post": {"summary": "Change user password", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}}, "required": ["oldPassword", "newPassword"]}}}}, "responses": {"200": {"description": "Password changed successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/users/theme-customization": {"post": {"summary": "Update theme customization settings for a user", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"userId": {"type": "string", "description": "The ID of the user"}, "background_color": {"type": "string"}, "theme_bg_color": {"type": "string"}, "feature_box_bg": {"type": "string"}, "background_light": {"type": "string"}, "border_color": {"type": "string"}, "circle_color": {"type": "string"}, "contest_block_bg": {"type": "string"}, "dark_text": {"type": "string"}, "faq_border": {"type": "string"}, "font_secondary": {"type": "string"}, "light_secondary_color": {"type": "string"}, "primary_color": {"type": "string"}, "progress_color": {"type": "string"}, "secondary_color": {"type": "string"}, "secondary_dark_color": {"type": "string"}, "table_header": {"type": "string"}, "input_bg": {"type": "string"}, "font_primary": {"type": "string"}}, "required": ["userId", "background_color", "theme_bg_color", "feature_box_bg", "background_light", "border_color", "circle_color", "contest_block_bg", "dark_text", "faq_border", "font_secondary", "light_secondary_color", "primary_color", "progress_color", "secondary_color", "secondary_dark_color", "table_header", "input_bg", "font_primary"]}}}}, "responses": {"200": {"description": "Theme customization updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Theme customization updated successfully."}}}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Unauthorized - Invalid or missing token"}, "500": {"description": "Server error"}}}}, "/admin/v1/users/get-theme-customization": {"post": {"summary": "Get theme customization details for a user", "tags": ["User"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"userId": {"type": "string", "description": "The ID of the user"}}, "required": ["userId"]}}}}, "responses": {"200": {"description": "Theme customization retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"themeSettings": {"type": "object", "example": {"primaryColor": "#ff0000", "secondaryColor": "#000000", "backgroundColor": "#f0f0f0"}}}}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Unauthorized - Invalid or missing token"}, "500": {"description": "Server error"}}}}, "/admin/v1/vendor/create": {"post": {"summary": "Create a new vendor", "tags": ["<PERSON><PERSON><PERSON>"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "id", "type": "string", "required": false, "description": "Optional vendor ID"}, {"in": "formData", "name": "email", "type": "string", "required": true, "description": "Vendor email"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Vendor phone number"}, {"in": "formData", "name": "name", "type": "string", "required": true, "description": "Vendor name"}, {"in": "formData", "name": "wallet_check_api", "type": "string", "required": true, "description": "Wallet check API URL"}, {"in": "formData", "name": "deposit_api", "type": "string", "required": true, "description": "Deposit API URL"}, {"in": "formData", "name": "balance_api", "type": "string", "required": true, "description": "Balance API URL"}, {"in": "formData", "name": "cricket", "type": "integer", "required": true, "description": "Cricket support (0 or 1)"}, {"in": "formData", "name": "football", "type": "integer", "required": true, "description": "Football support (0 or 1)"}, {"in": "formData", "name": "player_accumulator", "type": "integer", "required": true, "description": "Player accumulator flag (0 or 1)"}, {"in": "formData", "name": "player_contest", "type": "integer", "required": true, "description": "Player contest flag (0 or 1)"}, {"in": "formData", "name": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "description": "API key of vendor"}, {"in": "formData", "name": "logo_url", "type": "string", "required": false, "description": "Vendor logo URL (optional)"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> created successfully"}, "400": {"description": "Validation error"}}}}, "/admin/v1/vendor/login": {"post": {"summary": "Authenticate a vendor", "tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "description": "Vendor email"}, "password": {"type": "string", "description": "Vendor password"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful"}, "400": {"description": "Validation error"}, "401": {"description": "Invalid credentials"}}}}, "/admin/v1/wallet/list": {"get": {"summary": "Get list of wallets", "tags": ["Wallet"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Wallet list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/wallet/filter": {"post": {"summary": "Filter wallet records", "tags": ["Wallet"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Wallet filter results"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/wallet/update": {"post": {"summary": "Update wallet details", "tags": ["Wallet"], "consumes": ["application/x-www-form-urlencoded"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Wallet updated successfully"}, "401": {"description": "Unauthorized"}}}}, "/admin/v1/wallet/Withdraw/list": {"get": {"summary": "Get list of wallet withdrawals", "tags": ["Wallet"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Withdrawal list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/banners/list": {"post": {"summary": "Get list of banners", "tags": ["Banner"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "type", "type": "string", "required": true, "description": "Type of banners to retrieve"}, {"in": "formData", "name": "device", "type": "string", "required": false, "description": "Device type filter (optional)"}], "responses": {"200": {"description": "Banner list retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/banners/swaggerscroll": {"get": {"summary": "Get paginated banner scroll data", "tags": ["Banner"], "responses": {"200": {"description": "Banner scroll data retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/cms/list": {"post": {"summary": "Get list of CMS items", "tags": ["CMS"], "responses": {"200": {"description": "CMS list retrieved successfully"}}}}, "/app/v1/cms/category_list": {"get": {"summary": "Get list of CMS categories", "tags": ["CMS"], "responses": {"200": {"description": "CMS category list retrieved successfully"}}}}, "/app/v1/cms/faq_view": {"post": {"summary": "View FAQ list", "tags": ["CMS"], "responses": {"200": {"description": "FAQ list retrieved successfully"}}}}, "/app/v1/cms/web-setting": {"get": {"summary": "Get web settings", "tags": ["CMS"], "responses": {"200": {"description": "Web settings retrieved successfully"}}}}, "/app/v1/contactus/create": {"post": {"summary": "Create a new contact us message", "tags": ["ContactUs"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": true, "description": "Name of the person contacting"}, {"in": "formData", "name": "email", "type": "string", "required": true, "description": "Email address"}, {"in": "formData", "name": "message", "type": "string", "required": true, "description": "Message content"}, {"in": "formData", "name": "subject", "type": "string", "required": true, "description": "Subject of the message"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Phone number"}], "responses": {"200": {"description": "Contact message created successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/match/publish_match_list": {"post": {"summary": "Get list of active cricket matches based on status", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "number", "description": "Match status"}}, "required": ["r<PERSON>tus"]}}}}, "responses": {"200": {"description": "List of active cricket matches"}, "400": {"description": "Invalid input or missing parameters"}}}}, "/app/v1/match/publish_football_list": {"post": {"summary": "Get list of active football matches based on status", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "number", "description": "Match status"}}, "required": ["r<PERSON>tus"]}}}}, "responses": {"200": {"description": "List of active football matches"}, "400": {"description": "Invalid input or missing parameters"}}}}, "/app/v1/match/cricket/series": {"post": {"summary": "Get list of cricket series by status", "tags": ["Frontend_Series"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status of the series (e.g., active, completed)"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "List of cricket series retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/series": {"post": {"summary": "Get list of football series by status", "tags": ["Frontend_Series"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status of the series (e.g., active, completed)"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "List of football series retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/matches/myportfolio": {"post": {"summary": "Get cricket matches for user's portfolio filtered by status", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "number", "description": "Status filter for matches"}}, "required": ["r<PERSON>tus"]}}}}, "responses": {"200": {"description": "Cricket matches for portfolio retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/matches/myportfolio": {"post": {"summary": "Get football matches for user's portfolio filtered by status", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "number", "description": "Status filter for matches"}}, "required": ["r<PERSON>tus"]}}}}, "responses": {"200": {"description": "Football matches for portfolio retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/series/myportfolio": {"post": {"summary": "Get cricket series for user's portfolio filtered by status", "tags": ["Frontend_Series"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status filter for cricket series"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Cricket series for portfolio retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/series/myportfolio": {"post": {"summary": "Get football series for user's portfolio filtered by status", "tags": ["Frontend_Series"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "description": "Status filter for football series"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Football series for portfolio retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/player_list": {"post": {"summary": "Get player list filtered by type and match ID", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of players to list"}, "match_id": {"type": "number", "description": "ID of the match"}}, "required": ["type", "match_id"]}}}}, "responses": {"200": {"description": "Player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/save_player": {"post": {"summary": "Save player list for a match with team and role counts", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of players"}, "match_id": {"type": "number", "description": "Match ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Array of player IDs"}, "team_count": {"type": "object", "description": "Object containing team count details"}, "player_role_count": {"type": "object", "description": "Object containing player role count details"}}, "required": ["type", "match_id", "pid", "team_count", "player_role_count"]}}}}, "responses": {"200": {"description": "Player list saved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/accumulator/team_list": {"post": {"summary": "Get accumulator team list by type and league ID", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of accumulator teams"}, "league_id": {"type": "number", "description": "League ID"}}, "required": ["type", "league_id"]}}}}, "responses": {"200": {"description": "List of teams retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/accumulator/add_team": {"post": {"summary": "Add a team to accumulator with game details", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "gametype": {"type": "string", "description": "Type of the game"}, "gamekey": {"type": "string", "description": "Unique game key"}, "data": {"type": "array", "items": {"type": "object", "properties": {"team_id": {"type": "number", "description": "Team ID"}, "pamount": {"type": "number", "description": "Payment amount"}, "sharecnt": {"type": "number", "description": "Share count"}}, "required": ["team_id", "pamount", "sharecnt"]}}}, "required": ["league_id", "gametype", "gamekey", "data"]}}}}, "responses": {"200": {"description": "Team added successfully to accumulator"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/accumulator/add_plyaccumulator": {"post": {"summary": "Add player accumulator details for a match", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "gametype": {"type": "string", "description": "Type of the game"}, "gamekey": {"type": "string", "description": "Unique game key"}, "data": {"type": "array", "items": {"type": "object", "properties": {"pid": {"type": "number", "description": "Player ID"}, "gkamount": {"type": "number", "description": "Amount related to player accumulator"}, "sharecnt": {"type": "number", "description": "Share count"}}, "required": ["pid", "gkamount", "sharecnt"]}}}, "required": ["match_id", "gametype", "gamekey", "data"]}}}}, "responses": {"200": {"description": "Player accumulator added successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/accumulator/prize_pool_list": {"post": {"summary": "Get prize pool list for accumulator by league", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of accumulator"}, "league_id": {"type": "number", "description": "League ID"}}, "required": ["type", "league_id"]}}}}, "responses": {"200": {"description": "Prize pool list fetched successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/accumulator/add_prize_pool": {"post": {"summary": "Add prize pool for accumulator match", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "data": {"type": "array", "items": {"type": "object", "properties": {"team_id": {"type": "number", "description": "Team ID"}, "price": {"type": "number", "description": "Prize amount"}, "quality": {"type": "number", "description": "Quality of prize"}}}}}, "required": ["match_id", "data"]}}}}, "responses": {"200": {"description": "Prize pool added successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/player_detail": {"post": {"summary": "Get detailed info about a player", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"player_id": {"type": "number", "description": "Player ID"}, "type": {"type": "string", "description": "Type of the detail request"}, "seasonId": {"type": "string", "description": "Season ID (optional)"}, "match_id": {"type": "string", "description": "Match ID (optional)"}, "league_id": {"type": "number", "description": "League ID (optional)"}}, "required": ["player_id", "type"]}}}}, "responses": {"200": {"description": "Player detail fetched successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/series_player_detail": {"post": {"summary": "Get detailed info about a series player", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"player_id": {"type": "number", "description": "Player ID"}}, "required": ["player_id"]}}}}, "responses": {"200": {"description": "Series player detail fetched successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/save_series_player": {"post": {"summary": "Save players for a series", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series"}, "league_id": {"type": "number", "description": "League ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Player IDs array"}, "is_substitue": {"type": "number", "description": "Substitute status (1 or 0)"}, "team_count": {"type": "object", "description": "Team count object"}, "team_no": {"type": "number", "description": "Team number"}, "contest_id": {"type": "string", "description": "Contest ID (optional)"}}, "required": ["type", "league_id", "pid", "is_substitue"]}}}}, "responses": {"200": {"description": "Series players saved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/add_substitute_player_series": {"post": {"summary": "Add substitute player to series", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series"}, "league_id": {"type": "number", "description": "League ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Player IDs array"}, "is_substitue": {"type": "number", "description": "Substitute status (1 or 0)"}, "team_count": {"type": "object", "description": "Team count object"}, "team_no": {"type": "number", "description": "Team number"}, "uteamid": {"type": "string", "description": "User team ID"}}, "required": ["type", "league_id", "pid", "is_substitue", "uteamid"]}}}}, "responses": {"200": {"description": "Substitute player added successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/sereis_player_list": {"post": {"summary": "Get series player list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series"}, "league_id": {"type": "number", "description": "League ID"}, "contest_id": {"type": "string", "description": "Contest ID (optional)"}}, "required": ["type", "league_id"]}}}}, "responses": {"200": {"description": "List of players in the series"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/team": {"post": {"summary": "Get cricket team list for user", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "poolid": {"type": "string", "nullable": true, "description": "Pool ID (optional)"}, "type": {"type": "string", "description": "Type of the team"}}, "required": ["match_id", "type"]}}}}, "responses": {"200": {"description": "List of teams for cricket match"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/team": {"post": {"summary": "Get football team list for user", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "poolid": {"type": "string", "nullable": true, "description": "Pool ID (optional)"}, "type": {"type": "string", "description": "Type of the team"}}, "required": ["match_id", "type"]}}}}, "responses": {"200": {"description": "List of teams for football match"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/seriesteam": {"post": {"summary": "Get cricket series team list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "poolid": {"type": "string", "nullable": true, "description": "Pool ID (optional)"}, "contest_id": {"type": "string", "nullable": true, "description": "Contest ID (optional)"}}, "required": ["league_id"]}}}}, "responses": {"200": {"description": "Cricket series team list returned successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/seriesteam": {"post": {"summary": "Get football series team list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "poolid": {"type": "string", "nullable": true, "description": "Pool ID (optional)"}, "contest_id": {"type": "string", "nullable": true, "description": "Contest ID (optional)"}}, "required": ["league_id"]}}}}, "responses": {"200": {"description": "Football series team list returned successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/edit_match_player_list": {"post": {"summary": "Edit match player list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of the match"}, "uteamid": {"type": "string", "description": "User team ID"}, "match_id": {"type": "number", "description": "Match ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Player IDs"}, "team_count": {"type": "object", "description": "Team count details (used in mobile)"}, "player_role_count": {"type": "object", "description": "Player role count details (used in mobile)"}, "team_no": {"type": "number", "description": "Team number"}}, "required": ["type", "uteamid", "match_id", "pid", "team_count", "player_role_count", "team_no"]}}}}, "responses": {"200": {"description": "Match player list edited successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/edit_series_player_list": {"post": {"summary": "Edit series player list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series"}, "uteamid": {"type": "string", "description": "User team ID"}, "league_id": {"type": "number", "description": "League ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Player IDs"}, "team_count": {"type": "object", "description": "Team count details"}, "is_substitue": {"type": "number", "description": "Substitute flag (0 or 1)"}, "team_no": {"type": "number", "description": "Team number"}}, "required": ["type", "uteamid", "league_id", "pid", "team_count", "is_substitue"]}}}}, "responses": {"200": {"description": "Series player list edited successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/user_match_player_list": {"post": {"summary": "Get cricket user match player list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}, "smtype": {"type": "string", "description": "Optional smtype"}, "type": {"type": "string", "description": "Optional type"}}, "required": ["uteamid"]}}}}, "responses": {"200": {"description": "Cricket user match player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/user_match_player_list": {"post": {"summary": "Get football user match player list", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}, "smtype": {"type": "string", "description": "Optional smtype"}, "type": {"type": "string", "description": "Optional type"}}, "required": ["uteamid"]}}}}, "responses": {"200": {"description": "Football user match player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/match/clone_player_list": {"post": {"summary": "Clone cricket match player list by user team ID and team number", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}, "team_no": {"type": "number", "description": "Team number"}}, "required": ["uteamid", "team_no"]}}}}, "responses": {"200": {"description": "Cloned cricket match player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/match/clone_player_list": {"post": {"summary": "Clone football match player list by user team ID and team number", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}, "team_no": {"type": "number", "description": "Team number"}}, "required": ["uteamid", "team_no"]}}}}, "responses": {"200": {"description": "Cloned football match player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/cricket/series/clone_player_list": {"post": {"summary": "Clone cricket series player list by user team ID", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}}, "required": ["uteamid"]}}}}, "responses": {"200": {"description": "Cloned cricket series player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/football/series/clone_player_list": {"post": {"summary": "Clone football series player list by user team ID", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"uteamid": {"type": "string", "description": "User team ID"}}, "required": ["uteamid"]}}}}, "responses": {"200": {"description": "Cloned football series player list retrieved successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/add_coins": {"post": {"summary": "Add coins to user account", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"coins": {"type": "number", "description": "Number of coins to add"}}, "required": ["coins"]}}}}, "responses": {"200": {"description": "Coins added successfully"}, "400": {"description": "Missing or invalid parameters"}}}}, "/app/v1/match/live_score": {"post": {"summary": "Get live cricket match score", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Live cricket score fetched successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/fb_live_score": {"post": {"summary": "Get live football match score", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Live football score fetched successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/commentary_score_list": {"post": {"summary": "Get commentary score list for a match", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "ID of the match"}, "type": {"type": "string", "description": "Type of the match ('ckt' for cricket, 'fb' for football)"}}, "required": ["match_id", "type"]}}}}, "responses": {"200": {"description": "Commentary score list fetched successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/team_profile_detail": {"post": {"summary": "Get detailed profile of a team", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"gametype": {"type": "string", "description": "Type of game (e.g., cricket, football)"}, "team_id": {"type": "number", "description": "ID of the team"}}, "required": ["gametype", "team_id"]}}}}, "responses": {"200": {"description": "Team profile details retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/series_team_stats_list": {"post": {"summary": "Get team stats list for a series", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"cid": {"type": "number", "description": "Series ID"}, "type": {"type": "string", "description": "Type of sport or game"}}, "required": ["cid", "type"]}}}}, "responses": {"200": {"description": "Team stats list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/series_match_list": {"post": {"summary": "Get list of matches in a series", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"cid": {"type": "number", "description": "Series ID"}, "type": {"type": "string", "description": "Type of sport or game"}}, "required": ["cid", "type"]}}}}, "responses": {"200": {"description": "List of matches retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/match/update_substitute_player_series": {"post": {"summary": "Update substitute players in a series", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "pid": {"type": "array", "items": {"type": "number"}, "description": "Array of player IDs"}, "uteamid": {"type": "string", "description": "User team ID"}, "match_id": {"type": "number", "description": "Match ID"}, "contest_id": {"type": "string", "description": "Contest ID"}, "type": {"type": "string", "description": "Type of the series or game"}}, "required": ["league_id", "pid", "uteamid", "match_id", "contest_id", "type"]}}}}, "responses": {"200": {"description": "Substitute players updated successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/notification/list": {"post": {"summary": "Get list of notifications", "tags": ["Notification"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Notification list retrieved successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/payment/createOrder": {"post": {"summary": "Create a payment order", "tags": ["Payment"], "responses": {"200": {"description": "Order created successfully"}}}}, "/app/v1/payment/createOrderInTrans": {"post": {"summary": "Create a payment order within a transaction", "tags": ["Payment"], "responses": {"200": {"description": "Order created in transaction successfully"}}}}, "/app/v1/payment/verifyOrder": {"post": {"summary": "Verify a payment order", "tags": ["Payment"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Order verified successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/payment/phonepepay": {"post": {"summary": "Initiate PhonePe payment", "tags": ["Payment"], "responses": {"200": {"description": "PhonePe payment initiated successfully"}}}}, "/app/v1/payment/responsephonepe": {"post": {"summary": "Handle PhonePe payment response", "tags": ["Payment"], "responses": {"200": {"description": "PhonePe payment response handled successfully"}}}}, "/app/v1/payment/phonepepay/mobile": {"post": {"summary": "Initiate PhonePe payment on mobile", "tags": ["Payment"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "PhonePe mobile payment initiated successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/payment/responsephonepe/mobile": {"post": {"summary": "Handle PhonePe mobile payment response", "tags": ["Payment"], "responses": {"200": {"description": "PhonePe mobile payment response handled successfully"}}}}, "/app/v1/pool/matches/cricket/pool_contest_list": {"post": {"summary": "Get list of cricket pool contests filtered by various parameters", "tags": ["Frontend_Match"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "ID of the match", "example": 123}, "uptojoinmin": {"type": "number", "description": "Minimum join count filter", "example": 10}, "uptojoinmax": {"type": "number", "description": "Maximum join count filter", "example": 100}, "entrymax": {"type": "number", "description": "Maximum entry fee filter", "example": 500}, "emtrymin": {"type": "number", "description": "Minimum entry fee filter", "example": 100}, "prizepoolmax": {"type": "number", "description": "Maximum prize pool filter", "example": 10000}, "prizepoolmin": {"type": "number", "description": "Minimum prize pool filter", "example": 1000}, "gurantee": {"type": "number", "description": "Guarantee amount filter", "example": 1}, "contestType": {"type": "string", "description": "Type of contest", "example": "free"}, "contestid": {"type": "string", "description": "Specific contest ID", "example": "CONTEST123"}, "isprivate": {"type": "number", "description": "Private contest flag (0 or 1)", "example": 0}}, "required": ["match_id"]}}}}, "responses": {"200": {"description": "List of pool contests"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/pool/filter": {"post": {"summary": "Filter cricket contests by match ID and other criteria", "tags": ["Frontend_PoolContest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID to filter contests", "example": 123}}, "required": ["match_id"]}}}}, "responses": {"200": {"description": "Filtered list of cricket contests"}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/pool/matches/football/pool_contest_list": {"post": {"summary": "Get football pool contest list with filters", "tags": ["Frontend_PoolContest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Football match ID"}, "uptojoinmin": {"type": "number", "description": "Minimum join limit"}, "uptojoinmax": {"type": "number", "description": "Maximum join limit"}, "entrymax": {"type": "number", "description": "Maximum entry amount"}, "emtrymin": {"type": "number", "description": "Minimum entry amount"}, "prizepoolmax": {"type": "number", "description": "Maximum prize pool amount"}, "prizepoolmin": {"type": "number", "description": "Minimum prize pool amount"}, "gurantee": {"type": "number", "description": "Guarantee amount"}, "contestType": {"type": "string", "description": "Type of contest"}, "contestid": {"type": "string", "description": "Contest ID"}, "isprivate": {"type": "number", "description": "Private contest flag (1 or 0)"}}, "required": ["match_id"]}}}}, "responses": {"200": {"description": "List of football pool contests filtered by criteria"}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/cricket/pool_contest_list": {"post": {"summary": "Get cricket series pool contest list filtered by league and status", "tags": ["Frontend_PoolContest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID for cricket series"}, "status": {"type": "string", "description": "Status filter (optional)"}, "contest_id": {"type": "string", "description": "Contest ID filter (optional)"}}, "required": ["league_id"]}}}}, "responses": {"200": {"description": "List of cricket series pool contests"}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/football/pool_contest_list": {"post": {"summary": "Get football series pool contest list filtered by league and status", "tags": ["Frontend_PoolContest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID for football series"}, "status": {"type": "string", "description": "Status filter (optional)"}, "contest_id": {"type": "string", "description": "Contest ID filter (optional)"}}, "required": ["league_id"]}}}}, "responses": {"200": {"description": "List of football series pool contests"}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/match/cricket/pool_detail": {"post": {"summary": "Get cricket match pool details by match and pool ID", "tags": ["Frontend_PoolDetail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "ID of the cricket match"}, "pool_id": {"type": "string", "description": "ID of the pool"}, "status": {"type": "number", "nullable": true, "description": "Status filter (optional)"}, "userid": {"type": "number", "nullable": true, "description": "User ID (optional)"}}, "required": ["match_id", "pool_id"]}}}}, "responses": {"200": {"description": "Cricket pool detail retrieved successfully"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/match/football/pool_detail": {"post": {"summary": "Get football match pool details by match and pool ID", "tags": ["Frontend_PoolDetail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "ID of the football match"}, "pool_id": {"type": "string", "description": "ID of the pool"}, "status": {"type": "number", "nullable": true, "description": "Status filter (optional)"}, "userid": {"type": "number", "nullable": true, "description": "User ID (optional)"}}, "required": ["match_id", "pool_id"]}}}}, "responses": {"200": {"description": "Football pool detail retrieved successfully"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/cricket/pool_detail": {"post": {"summary": "Get cricket series pool details by league and pool ID", "tags": ["Frontend_PoolDetail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID for the cricket series"}, "pool_id": {"type": "string", "description": "Pool ID within the series"}}, "required": ["league_id", "pool_id"]}}}}, "responses": {"200": {"description": "Cricket series pool details retrieved successfully"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/football/pool_detail": {"post": {"summary": "Get football series pool details by league and pool ID", "tags": ["Frontend_PoolDetail"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID for the football series"}, "pool_id": {"type": "string", "description": "Pool ID within the series"}}, "required": ["league_id", "pool_id"]}}}}, "responses": {"200": {"description": "Football series pool details retrieved successfully"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/join_pool": {"post": {"summary": "Join a pool contest", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"poolid": {"type": "string", "description": "ID of the pool to join"}, "match_id": {"type": "number", "description": "Match ID associated with the pool"}, "gametype": {"type": "string", "description": "Type of game (e.g., cricket, football)"}, "uteamid": {"type": "string", "description": "User's team ID"}}, "required": ["poolid", "match_id", "gametype", "uteamid"]}}}}, "responses": {"200": {"description": "Successfully joined the pool contest"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/confirm_join_pre": {"post": {"summary": "Preview prize details before joining a pool", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"pool_id": {"type": "string", "description": "ID of the pool"}, "user_id": {"type": "string", "description": "User ID"}}, "required": ["pool_id", "user_id"]}}}}, "responses": {"200": {"description": "Prize join preview details returned successfully"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/matches/cricket/mypool": {"post": {"summary": "Get user's cricket pool contests for a match", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "uptojoinmin": {"type": "number", "description": "Minimum join limit"}, "uptojoinmax": {"type": "number", "description": "Maximum join limit"}, "entrymax": {"type": "number", "description": "Maximum entry fee"}, "emtrymin": {"type": "number", "description": "Minimum entry fee"}, "prizepoolmax": {"type": "number", "description": "Maximum prize pool"}, "prizepoolmin": {"type": "number", "description": "Minimum prize pool"}, "gurantee": {"type": "number", "description": "Guarantee amount"}, "contestType": {"type": "string", "description": "Type of contest"}, "contestid": {"type": "string", "description": "Contest ID"}, "isprivate": {"type": "number", "description": "Private contest flag"}}}}}}, "responses": {"200": {"description": "List of user's cricket pool contests for the match"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/matches/football/mypool": {"post": {"summary": "Get user's football pool contests for a match", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "uptojoinmin": {"type": "number", "description": "Minimum join limit"}, "uptojoinmax": {"type": "number", "description": "Maximum join limit"}, "entrymax": {"type": "number", "description": "Maximum entry fee"}, "emtrymin": {"type": "number", "description": "Minimum entry fee"}, "prizepoolmax": {"type": "number", "description": "Maximum prize pool"}, "prizepoolmin": {"type": "number", "description": "Minimum prize pool"}, "gurantee": {"type": "number", "description": "Guarantee amount"}, "contestType": {"type": "string", "description": "Type of contest"}, "contestid": {"type": "string", "description": "Contest ID"}, "isprivate": {"type": "number", "description": "Private contest flag"}}}}}}, "responses": {"200": {"description": "List of user's football pool contests for the match"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/cricket/mypool": {"post": {"summary": "Get user's cricket pool contests for a series", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "status": {"type": "string", "description": "Status filter (optional)"}, "contest_id": {"type": "string", "description": "Contest ID filter (optional)"}}}}}}, "responses": {"200": {"description": "List of user's cricket pool contests for the series"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/series/football/mypool": {"post": {"summary": "Get user's football pool contests for a series", "tags": ["Frontend_Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "League ID"}, "status": {"type": "string", "description": "Status filter (optional)"}, "contest_id": {"type": "string", "description": "Contest ID filter (optional)"}}}}}}, "responses": {"200": {"description": "List of user's football pool contests for the series"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/privatecontest/contestsize": {"post": {"summary": "Get list of private contest size options", "tags": ["Frontend_Private Contest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"contest_size": {"type": "number", "description": "Desired size of the contest", "example": 10}}}}}}, "responses": {"200": {"description": "List of contest size options"}, "400": {"description": "Invalid input parameters"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/privatecontest/save": {"post": {"summary": "Save a private contest", "tags": ["Frontend_Private Contest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID"}, "privatename": {"type": "string", "description": "Name of the private contest"}, "maxteams": {"type": "number", "description": "Maximum number of teams allowed"}, "joinfee": {"type": "number", "description": "Joining fee for the contest"}, "winners": {"type": "number", "description": "Number of winners"}, "s": {"type": "number", "description": "S parameter (specific use case)"}, "m": {"type": "number", "description": "M parameter (specific use case)"}, "totalwinamt": {"type": "number", "description": "Total winning amount"}, "gtype": {"type": "string", "description": "Game type (e.g., ckt or fb)"}, "poolpb": {"type": "array", "items": {"type": "object", "properties": {"pmin": {"type": "number"}, "pmax": {"type": "number"}, "pamount": {"type": "number"}}}}}}}}}, "responses": {"200": {"description": "Private contest saved successfully"}, "400": {"description": "Invalid input data"}, "401": {"description": "Unauthorized access"}}}}, "/app/v1/pool/match/poolprizebreak": {"post": {"summary": "Save prize break information for a match pool", "tags": ["Frontend_Pool Prize Break"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "description": "Prize break details for the match pool"}}}}, "responses": {"200": {"description": "Prize break data saved successfully"}, "400": {"description": "Invalid data provided"}, "500": {"description": "Internal server error"}}}}, "/app/v1/pool/sereis/privatecontest/save": {"post": {"summary": "Save a private contest for a series", "tags": ["Frontend_Series Private Contest"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "number", "description": "ID of the league"}, "privatename": {"type": "string", "description": "Name of the private contest"}, "maxteams": {"type": "number", "description": "Maximum number of teams"}, "joinfee": {"type": "number", "description": "Joining fee"}, "winners": {"type": "number", "description": "Number of winners"}, "s": {"type": "number", "description": "Some parameter (e.g., series flag)"}, "m": {"type": "number", "description": "Another parameter (e.g., mode/flag)"}, "date_start": {"type": "string", "description": "Start date of the contest (YYYY-MM-DD)"}, "date_end": {"type": "string", "description": "End date of the contest (YYYY-MM-DD)"}, "gtype": {"type": "string", "description": "Game type"}, "totalwinamt": {"type": "number", "description": "Total winning amount"}, "poolpb": {"type": "array", "description": "Pool prize breakup", "items": {"type": "object", "properties": {"pmin": {"type": "number", "description": "Minimum rank"}, "pmax": {"type": "number", "description": "Maximum rank"}, "pamount": {"type": "number", "description": "Prize amount"}}}}}}}}}, "responses": {"200": {"description": "Series private contest saved successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/app/v1/pool/join_pool_series": {"post": {"summary": "Join a pool contest for a series", "tags": ["Frontend_Series Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"poolid": {"type": "string", "description": "ID of the pool contest"}, "league_id": {"type": "number", "description": "ID of the league (series)"}, "gametype": {"type": "string", "description": "Game type (e.g., cricket, football)"}, "uteamid": {"type": "string", "description": "User team ID to join the contest"}}}}}}, "responses": {"200": {"description": "Joined pool contest successfully"}, "400": {"description": "Invalid request parameters"}, "401": {"description": "Unauthorized access"}, "500": {"description": "Server error"}}}}, "/app/v1/pool/confirm_join_pre_series": {"post": {"summary": "Confirm join preview for a series pool contest", "tags": ["Frontend_Series Pool"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"pool_id": {"type": "string", "description": "ID of the series pool contest"}, "user_id": {"type": "string", "description": "ID of the user requesting join preview"}}}}}}, "responses": {"200": {"description": "Join preview returned successfully"}, "400": {"description": "Invalid request data"}, "401": {"description": "Unauthorized access"}, "500": {"description": "Internal server error"}}}}, "/app/v1/pool/match_contest": {"post": {"summary": "Get contest list for a specific match", "tags": ["Frontend_Match Contest"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "ID of the match to retrieve contests for", "example": 101}}}}}}, "responses": {"200": {"description": "Successfully retrieved contest list"}, "400": {"description": "Invalid match ID provided"}, "500": {"description": "Internal server error"}}}}, "/app/v1/pool/matches/user_team_player_list": {"post": {"summary": "Get player list of a user's team for a match", "tags": ["Frontend_User Team"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "number", "description": "Match ID", "example": 123}, "uteamid": {"type": "string", "description": "User team ID", "example": "team_456"}, "gametype": {"type": "string", "description": "Game type (e.g., cricket or football)", "example": "cricket"}, "type": {"type": "string", "description": "Optional type parameter"}, "contest_id": {"type": "string", "description": "Optional contest ID"}}}}}}, "responses": {"200": {"description": "Player list retrieved successfully"}, "400": {"description": "Invalid request data"}, "500": {"description": "Internal server error"}}}}, "/app/v1/users/get_profile": {"post": {"summary": "Get user profile by user ID", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userid": {"type": "string", "example": "user123", "description": "User ID to fetch profile"}}}}}}, "responses": {"200": {"description": "User profile retrieved successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/register": {"post": {"summary": "Register a new user", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "email", "type": "string", "required": false, "description": "User email (optional)"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "User phone number (8-12 characters)"}, {"in": "formData", "name": "password", "type": "string", "required": false, "description": "User password (optional)"}, {"in": "formData", "name": "country_code", "type": "string", "required": true, "description": "Country code"}, {"in": "formData", "name": "usertype", "type": "integer", "required": true, "description": "User type"}, {"in": "formData", "name": "logintype", "type": "string", "required": true, "description": "Login type"}, {"in": "formData", "name": "socialid", "type": "string", "required": false, "description": "Social ID (optional)"}, {"in": "formData", "name": "socialtype", "type": "integer", "required": false, "description": "Social type (optional)"}, {"in": "formData", "name": "referral", "type": "string", "required": false, "description": "Referral code (optional)"}, {"in": "formData", "name": "referred_by", "type": "string", "required": false, "description": "Referred by (optional)"}, {"in": "formData", "name": "devicetype", "type": "string", "required": false, "description": "Device type (optional)"}, {"in": "formData", "name": "devicetoken", "type": "string", "required": false, "description": "Device token (optional)"}, {"in": "formData", "name": "lat", "type": "number", "required": false, "description": "Latitude (optional)"}, {"in": "formData", "name": "long", "type": "number", "required": false, "description": "Longitude (optional)"}, {"in": "formData", "name": "state_name", "type": "string", "required": false, "description": "State name (optional)"}], "responses": {"200": {"description": "User registered successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/login": {"post": {"summary": "User login (front-end)", "tags": ["FrontendUser"], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"type": "object", "required": ["usertype"], "properties": {"usertype": {"type": "integer", "description": "User type"}, "country_code": {"type": "string", "description": "Country code (optional)"}, "phone": {"type": "string", "description": "Phone number (optional)"}, "password": {"type": "string", "description": "Password (optional)"}, "email": {"type": "string", "description": "Email (optional)"}, "socialid": {"type": "string", "description": "Social ID (optional)"}, "socialtype": {"type": "integer", "description": "Social type (optional)"}, "devicetype": {"type": "string", "description": "Device type (optional)"}, "devicetoken": {"type": "string", "description": "Device token (optional)"}, "lat": {"type": "number", "description": "Latitude (optional)"}, "long": {"type": "number", "description": "Longitude (optional)"}, "state_name": {"type": "string", "description": "State name (optional)"}}}}], "responses": {"200": {"description": "User authenticated successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/social_login": {"post": {"summary": "User social login", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "usertype", "type": "integer", "required": true, "description": "User type"}, {"in": "formData", "name": "country_code", "type": "string", "required": false, "description": "Country code (optional)"}, {"in": "formData", "name": "phone", "type": "string", "required": false, "description": "Phone number (optional)"}, {"in": "formData", "name": "password", "type": "string", "required": false, "description": "Password (optional)"}, {"in": "formData", "name": "email", "type": "string", "required": false, "description": "Email (optional)"}, {"in": "formData", "name": "socialid", "type": "string", "required": true, "description": "Social ID"}, {"in": "formData", "name": "socialtype", "type": "integer", "required": true, "description": "Social type"}, {"in": "formData", "name": "devicetype", "type": "string", "required": false, "description": "Device type (optional)"}, {"in": "formData", "name": "devicetoken", "type": "string", "required": false, "description": "Device token (optional)"}, {"in": "formData", "name": "lat", "type": "number", "required": false, "description": "Latitude (optional)"}, {"in": "formData", "name": "long", "type": "number", "required": false, "description": "Longitude (optional)"}, {"in": "formData", "name": "state_name", "type": "string", "required": false, "description": "State name (optional)"}, {"in": "formData", "name": "profilepic", "type": "string", "required": false, "description": "Profile picture URL (optional)"}], "responses": {"200": {"description": "Social login successful"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/send_app_link": {"post": {"summary": "Send app link to user", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country_code", "type": "string", "required": false, "description": "Country code (optional)"}, {"in": "formData", "name": "phone", "type": "string", "required": false, "description": "Phone number (optional)"}], "responses": {"200": {"description": "App link sent successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/update_token": {"post": {"summary": "Update user's device token", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Device token updated successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/verifyphone": {"post": {"summary": "Verify user phone number", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country_code", "type": "string", "required": true, "description": "Country code"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Phone number"}, {"in": "formData", "name": "otp", "type": "integer", "required": false, "description": "One-time password (optional)"}, {"in": "formData", "name": "socialid", "type": "string", "required": false, "description": "Social ID (optional)"}, {"in": "formData", "name": "profilepic", "type": "string", "required": false, "description": "Profile picture URL (optional)"}, {"in": "formData", "name": "isVerifed", "type": "boolean", "required": false, "description": "Verification status (optional)"}, {"in": "formData", "name": "name", "type": "string", "required": false, "description": "User name (optional)"}, {"in": "formData", "name": "ip", "type": "string", "required": false, "description": "IP address (optional)"}, {"in": "formData", "name": "timezone", "type": "string", "required": false, "description": "Timezone (optional)"}, {"in": "formData", "name": "deviceId", "type": "string", "required": false, "description": "Device ID (optional)"}, {"in": "formData", "name": "rdevicetype", "type": "string", "required": false, "description": "Device type (optional)"}], "responses": {"200": {"description": "Phone verified successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/resend": {"post": {"summary": "Resend OTP to user phone", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country_code", "type": "string", "required": true, "description": "Country code"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Phone number"}], "responses": {"200": {"description": "OTP resent successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/forgot": {"post": {"summary": "Forgot password - send OTP", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country_code", "type": "string", "required": true, "description": "Country code"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Phone number"}], "responses": {"200": {"description": "OTP sent for password reset"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/verifyotp": {"post": {"summary": "Verify OTP for user", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country_code", "type": "string", "required": true, "description": "Country code"}, {"in": "formData", "name": "phone", "type": "string", "required": true, "description": "Phone number"}, {"in": "formData", "name": "otp", "type": "integer", "required": false, "description": "One-time password (optional)"}, {"in": "formData", "name": "socialid", "type": "string", "required": false, "description": "Social ID (optional)"}, {"in": "formData", "name": "profilepic", "type": "string", "required": false, "description": "Profile picture URL (optional)"}, {"in": "formData", "name": "name", "type": "string", "required": false, "description": "User name (optional)"}, {"in": "formData", "name": "isVerifed", "type": "boolean", "required": false, "description": "Verification status (optional)"}, {"in": "formData", "name": "ip", "type": "string", "required": false, "description": "IP address (optional)"}, {"in": "formData", "name": "timezone", "type": "string", "required": false, "description": "Timezone (optional)"}, {"in": "formData", "name": "deviceId", "type": "string", "required": false, "description": "Device ID (optional)"}, {"in": "formData", "name": "rdevicetype", "type": "string", "required": false, "description": "Device type (optional)"}], "responses": {"200": {"description": "OTP verified successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/reset": {"post": {"summary": "Reset user password", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "password", "type": "string", "required": true, "description": "New password (min 6 characters)"}], "responses": {"200": {"description": "Password reset successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/verifyemail": {"post": {"summary": "Verify user email", "tags": ["FrontendUser"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "email", "type": "string", "required": true, "description": "User email address"}], "responses": {"200": {"description": "Email verified successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/completeprofile": {"post": {"summary": "Complete user profile", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "name", "type": "string", "required": true, "description": "Full name"}, {"in": "formData", "name": "dob", "type": "string", "required": true, "description": "Date of birth (YYYY-MM-DD)"}, {"in": "formData", "name": "gender", "type": "string", "required": true, "description": "Gender"}, {"in": "formData", "name": "address", "type": "string", "required": true, "description": "Address"}, {"in": "formData", "name": "cityid", "type": "integer", "required": true, "description": "City ID"}, {"in": "formData", "name": "stateid", "type": "integer", "required": true, "description": "State ID"}, {"in": "formData", "name": "pincode", "type": "integer", "required": true, "description": "Postal code"}], "responses": {"200": {"description": "Profile completed successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/update_profile": {"post": {"summary": "Update user profile", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "userid", "type": "string", "required": true, "description": "User ID"}, {"in": "formData", "name": "name", "type": "string", "required": false, "description": "Full name"}, {"in": "formData", "name": "dob", "type": "string", "required": false, "description": "Date of birth (YYYY-MM-DD)"}, {"in": "formData", "name": "gender", "type": "string", "required": false, "description": "Gender"}, {"in": "formData", "name": "address", "type": "string", "required": false, "description": "Address"}, {"in": "formData", "name": "cityid", "type": "integer", "required": false, "description": "City ID"}, {"in": "formData", "name": "stateid", "type": "integer", "required": false, "description": "State ID"}, {"in": "formData", "name": "pincode", "type": "string", "required": false, "description": "Postal code"}, {"in": "formData", "name": "profilepic", "type": "string", "required": false, "description": "Profile picture URL"}], "responses": {"200": {"description": "Profile updated successfully"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/city": {"post": {"summary": "Get list of cities by state", "tags": ["FrontendLocation"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "state", "type": "string", "required": true, "description": "State name"}], "responses": {"200": {"description": "List of cities returned successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/state": {"post": {"summary": "Get list of states by country", "tags": ["FrontendLocation"], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "country", "type": "string", "required": true, "description": "Country name"}], "responses": {"200": {"description": "List of states returned successfully"}, "400": {"description": "Validation error"}}}}, "/app/v1/users/verification": {"post": {"summary": "Verify user details based on type", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["multipart/form-data"], "requestBody": {"content": {"multipart/form-data": {"schema": {"oneOf": [{"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["Indian", "British"]}}}, {"type": "object", "required": ["userid", "type", "panname", "dob"], "properties": {"userid": {"type": "integer"}, "type": {"type": "string", "enum": ["Indian"]}, "panimage": {"type": "string", "format": "binary", "description": "Optional PAN image file upload"}, "panname": {"type": "string"}, "dob": {"type": "string", "description": "Date of birth"}}}, {"type": "object", "required": ["userid", "type"], "properties": {"userid": {"type": "integer"}, "type": {"type": "string", "enum": ["British"]}}}]}}}}, "responses": {"200": {"description": "Verification successful"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/bankverification": {"post": {"summary": "Verify bank details of user", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "userid", "type": "integer", "required": true, "description": "User ID"}, {"in": "formData", "name": "bankname", "type": "string", "required": true, "description": "Bank name"}, {"in": "formData", "name": "ifsccode", "type": "string", "required": true, "description": "IFSC code of bank"}, {"in": "formData", "name": "acholdername", "type": "string", "required": true, "description": "Account holder name"}, {"in": "formData", "name": "acno", "type": "string", "required": true, "description": "Account number"}, {"in": "formData", "name": "upi", "type": "string", "required": false, "description": "UPI ID (optional)"}], "responses": {"200": {"description": "Bank verification successful"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/upiverification": {"post": {"summary": "Verify UPI details of user", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "parameters": [{"in": "formData", "name": "userid", "type": "integer", "required": true, "description": "User ID"}, {"in": "formData", "name": "upi", "type": "string", "required": true, "description": "UPI ID to verify"}], "responses": {"200": {"description": "UPI verification successful"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/view": {"get": {"summary": "View user transaction details", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Transaction details fetched successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/wallet_view": {"get": {"summary": "View user wallet details", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Wallet details fetched successfully"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/persona_verify": {"post": {"summary": "Verify personal details of a user", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/json"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["persona_id", "persona_status", "id_data"], "properties": {"persona_id": {"type": "string", "description": "Persona ID"}, "persona_status": {"type": "string", "description": "Status of the persona verification"}, "id_data": {"type": "object", "description": "Object containing ID data"}}}}}}, "responses": {"200": {"description": "Personal verification successful"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/sure_verify": {"post": {"summary": "SurePay verification of a user", "tags": ["FrontendUser"], "security": [{"bearerAuth": []}], "consumes": ["application/x-www-form-urlencoded"], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "Verification type"}, "idno": {"type": "string", "description": "ID number"}, "client_id": {"type": "string", "description": "Client ID"}, "otp": {"type": "string", "description": "OTP for verification"}}}}}}, "responses": {"200": {"description": "Sure verification completed"}, "400": {"description": "Validation error"}, "401": {"description": "Unauthorized"}}}}, "/app/v1/users/cricket/uc": {"post": {"summary": "Get upcoming cricket matches", "tags": ["FrontendCricket"], "responses": {"200": {"description": "Upcoming match list"}}}}, "/app/v1/users/cricket/players": {"post": {"summary": "Get list of players for a cricket match", "tags": ["FrontendCricket"], "responses": {"200": {"description": "Players list"}}}}, "/app/v1/users/cricket/scores": {"post": {"summary": "Get detailed scores of a cricket match", "tags": ["FrontendCricket"], "responses": {"200": {"description": "Score details"}}}}, "/app/v1/users/cricket/scores/short": {"post": {"summary": "Get short score summary", "tags": ["FrontendCricket"], "responses": {"200": {"description": "Short score list"}}}}, "/app/v1/users/wallet/withdraw_request": {"post": {"summary": "Request wallet withdrawal", "tags": ["FrontendWallet"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> requested"}}}}, "/app/v1/users/invite_count": {"post": {"summary": "Get count of successful invites", "tags": ["FrontendReferral"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Invite count fetched"}}}}, "/app/v1/users/refer_earn": {"post": {"summary": "Calculate referral earnings", "tags": ["FrontendReferral"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Referral earnings calculated"}}}}, "/app/v1/users/refer_earn_user": {"get": {"summary": "Get referral earnings of a user", "tags": ["FrontendReferral"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User referral earnings"}}}}, "/app/v1/users/send-email-otp": {"post": {"summary": "Send OTP to email", "tags": ["FrontendAuthentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "OTP sent successfully"}, "400": {"description": "Invalid email or request format"}}}}, "/app/v1/users/verify-email-otp": {"post": {"summary": "Verify OTP sent to email", "tags": ["FrontendAuthentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "otp": {"type": "number", "example": 123456}}, "required": ["email", "otp"]}}}}, "responses": {"200": {"description": "Email verified successfully"}, "400": {"description": "Invalid OTP or email"}}}}, "/app/v1/users/a/{id}": {"get": {"summary": "Count API hit by ID", "tags": ["FrontendUser"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Unique identifier to count hit"}], "responses": {"200": {"description": "Hit count recorded"}, "400": {"description": "Invalid ID"}}}}, "/app/v1/users/email_test": {"get": {"summary": "Send test email", "tags": ["FrontendUser"], "responses": {"200": {"description": "Test email sent successfully"}, "500": {"description": "Error sending email"}}}}, "/app/v1/users/signin": {"get": {"summary": "Authenticate vendor using user ID and API key", "tags": ["FrontendUser"], "parameters": [{"in": "query", "name": "userid", "required": true, "schema": {"type": "string"}, "description": "User ID of the vendor"}, {"in": "query", "name": "apikey", "required": true, "schema": {"type": "string"}, "description": "API key for authentication"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> authenticated successfully"}, "400": {"description": "Missing or invalid parameters"}, "401": {"description": "Authentication failed"}}}}, "/socket/v1/match/publish_match_list": {"post": {"tags": ["Socket"], "summary": "Get active cricket match list for publishing", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "integer", "description": "Match status (e.g. 1, 2, or 3)", "default": 1}, "page": {"type": "integer", "default": 1}, "limit": {"type": "integer", "default": 10}}}}}}, "responses": {"200": {"description": "A list of upcoming matches"}, "400": {"description": "Bad request or validation error"}}}}, "/socket/v1/match/publish_match_football_list": {"post": {"tags": ["Socket"], "summary": "Get active football match list for publishing", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rstatus": {"type": "integer", "default": 1}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}, "responses": {"200": {"description": "A list of upcoming football matches"}, "400": {"description": "Bad request or validation error"}}}}, "/socket/v1/match/myfixliveresult": {"post": {"tags": ["Socket"], "summary": "Fix live Result", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {}}}}}, "responses": {"200": {"description": "A list of upcoming football matches"}, "400": {"description": "Bad request or validation error"}}}}, "/socket/v1/player/playerlist": {"post": {"summary": "View player list", "tags": ["Socket"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {}}}}}, "responses": {"200": {"description": "Player list fetched successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}}, "/socket/v1/pool/pooldetails": {"post": {"tags": ["Socket"], "summary": "Get pool details for a specific match", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "string", "example": "12345"}}}}}}, "responses": {"200": {"description": "Pool details fetched successfully"}, "400": {"description": "Bad request"}}}}, "/socket/v1/pool/pooldetailseries": {"post": {"tags": ["Socket"], "summary": "Get pool details for a specific series", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"series_id": {"type": "string", "example": "67890"}}}}}}, "responses": {"200": {"description": "Series pool details fetched successfully"}, "400": {"description": "Bad request"}}}}, "/socket/v1/pool/poolmatchcricketlist": {"post": {"tags": ["Socket"], "summary": "Get match cricket pool contest list", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Match cricket pool contests retrieved"}, "400": {"description": "Bad request"}}}}, "/socket/v1/pool/poolmatchfootballlist": {"post": {"tags": ["Socket"], "summary": "Get match football pool contest list", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"match_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Match football pool contests retrieved"}, "400": {"description": "Bad request"}}}}, "/socket/v1/pool/poolcricketlist": {"post": {"tags": ["Socket"], "summary": "Get cricket pool contest list for a series", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Series cricket pool contests retrieved"}, "400": {"description": "Bad request"}}}}, "/socket/v1/pool/poolfootballlist": {"post": {"tags": ["Socket"], "summary": "Get football pool contest list for a series", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"league_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Series football pool contests retrieved"}, "400": {"description": "Bad request"}}}}, "/socket/v1/accmulator/teamaccmulator": {"post": {"summary": "Process team accumulator data", "tags": ["Socket"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {}}}}}, "responses": {"200": {"description": "Team accumulator data processed successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}}, "/socket/v1/accmulator/poolpzaccmulator": {"post": {"summary": "Process pool PZ accumulator data", "tags": ["Socket"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {}}}}}, "responses": {"200": {"description": "Pool PZ accumulator data processed successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}}, "/socket/v1/accmulator/plyallaccumulator": {"post": {"summary": "Process all accumulator data", "tags": ["Socket"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {}}}}}, "responses": {"200": {"description": "all accumulator data processed successfully"}, "401": {"description": "Unauthorized"}, "500": {"description": "Server error"}}}}, "/socket/v1/cache/cachestorageset": {"post": {"tags": ["Socket"], "summary": "Set data in socket cache storage", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"key": {"type": "string", "example": "match_list"}, "value": {"type": "object", "description": "Data to store in cache"}}}}}}, "responses": {"200": {"description": "Data stored successfully"}, "400": {"description": "Bad request or validation error"}}}}, "/socket/v1/cache/cachestorageget": {"post": {"tags": ["Socket"], "summary": "Get data from socket cache storage", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"key": {"type": "string", "example": "match_list"}}}}}}, "responses": {"200": {"description": "Retrieved data from cache"}, "400": {"description": "Bad request"}, "404": {"description": "Key not found"}}}}}, "tags": [{"name": "Contests", "description": "Contest management endpoints"}, {"name": "Dashboard", "description": "Endpoints related to the admin dashboard"}, {"name": "MatchAccumulator"}, {"name": "ReportManagement"}, {"name": "Settings", "description": "Admin settings management"}, {"name": "StateManager"}, {"name": "Transactions"}]}